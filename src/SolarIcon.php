<?php

declare(strict_types=1);

namespace Monsefeledrisse\LaravelSolarIcons;

/**
 * Solar Icon Enum
 *
 * This enum contains all available Solar icons across all icon sets.
 * Each case represents a unique icon with its corresponding BladeUI Icons identifier.
 *
 * Usage:
 * - In Blade: <x-icon :name="SolarIcon::home->value" />
 * - With @svg: @svg(SolarIcon::home->value)
 * - In PHP: SolarIcon::home->value returns 'solar-linear-home'
 * - As Blade component: <x-solar-linear-home />
 *
 * Generated automatically on 2025-08-03 23:08:05
 * Total icons: 1235
 *
 * @package Monsefeledrisse\LaravelSolarIcons
 */
enum SolarIcon: string
{
    case accessibility = 'solar-bold-accessibility'; // Accessibility (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case accumulator = 'solar-bold-accumulator'; // Accumulator (available in: solar-bold, solar-linear)
    case addCircle = 'solar-bold-add_circle'; // add_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case addFolder = 'solar-bold-add_folder'; // add_folder (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case addSquare = 'solar-bold-add_square'; // add_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case adhesivePlaster = 'solar-bold-adhesive_plaster'; // adhesive_plaster (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case adhesivePlaster2 = 'solar-bold-adhesive_plaster2'; // adhesive_plaster2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbuds = 'solar-bold-airbuds'; // Airbuds (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsCase = 'solar-bold-airbuds_case'; // airbuds_case (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsCaseCharge = 'solar-bold-airbuds_case_charge'; // airbuds_case_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsCaseMinimalistic = 'solar-bold-airbuds_case_minimalistic'; // airbuds_case_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsCaseOpen = 'solar-bold-airbuds_case_open'; // airbuds_case_open (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsCharge = 'solar-bold-airbuds_charge'; // airbuds_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsCheck = 'solar-bold-airbuds_check'; // airbuds_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsLeft = 'solar-bold-airbuds_left'; // airbuds_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsRemove = 'solar-bold-airbuds_remove'; // airbuds_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case airbudsRight = 'solar-bold-airbuds_right'; // airbuds_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarm = 'solar-bold-alarm'; // Alarm (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarmAdd = 'solar-bold-alarm_add'; // alarm_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarmPause = 'solar-bold-alarm_pause'; // alarm_pause (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarmPlay = 'solar-bold-alarm_play'; // alarm_play (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarmRemove = 'solar-bold-alarm_remove'; // alarm_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarmSleep = 'solar-bold-alarm_sleep'; // alarm_sleep (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alarmTurnOff = 'solar-bold-alarm_turn_off'; // alarm_turn_off (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case album = 'solar-bold-album'; // Album (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignBottom = 'solar-bold-align_bottom'; // align_bottom (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignHorizontaSpacing = 'solar-bold-align_horizonta_spacing'; // align_horizonta_spacing (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignHorizontalCenter = 'solar-bold-align_horizontal_center'; // align_horizontal_center (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignLeft = 'solar-bold-align_left'; // align_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignRight = 'solar-bold-align_right'; // align_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignTop = 'solar-bold-align_top'; // align_top (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignVerticalCenter = 'solar-bold-align_vertical_center'; // align_vertical_center (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case alignVerticalSpacing = 'solar-bold-align_vertical_spacing'; // align_vertical_spacing (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case altArrowDown = 'solar-bold-alt_arrow_down'; // alt_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case altArrowLeft = 'solar-bold-alt_arrow_left'; // alt_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case altArrowRight = 'solar-bold-alt_arrow_right'; // alt_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case altArrowUp = 'solar-bold-alt_arrow_up'; // alt_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archive = 'solar-bold-archive'; // Archive (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archiveCheck = 'solar-bold-archive_check'; // archive_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archiveDown = 'solar-bold-archive_down'; // archive_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archiveDownMinimlistic = 'solar-bold-archive_down_minimlistic'; // archive_down_minimlistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archiveMinimalistic = 'solar-bold-archive_minimalistic'; // archive_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archiveUp = 'solar-bold-archive_up'; // archive_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case archiveUpMinimlistic = 'solar-bold-archive_up_minimlistic'; // archive_up_minimlistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case armchair = 'solar-bold-armchair'; // Armchair (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case armchair2 = 'solar-bold-armchair2'; // armchair2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowDown = 'solar-bold-arrow_down'; // arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowLeft = 'solar-bold-arrow_left'; // arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowLeftDown = 'solar-bold-arrow_left_down'; // arrow_left_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowLeftUp = 'solar-bold-arrow_left_up'; // arrow_left_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowRight = 'solar-bold-arrow_right'; // arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowRightDown = 'solar-bold-arrow_right_down'; // arrow_right_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowRightUp = 'solar-bold-arrow_right_up'; // arrow_right_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowToDownLeft = 'solar-bold-arrow_to_down_left'; // arrow_to_down_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowToDownRight = 'solar-bold-arrow_to_down_right'; // arrow_to_down_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowToTopLeft = 'solar-bold-arrow_to_top_left'; // arrow_to_top_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowToTopRight = 'solar-bold-arrow_to_top_right'; // arrow_to_top_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case arrowUp = 'solar-bold-arrow_up'; // arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case asteroid = 'solar-bold-asteroid'; // Asteroid (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case atom = 'solar-bold-atom'; // Atom (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case augmentedReality = 'solar-bold-augmented_reality'; // augmented_reality (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case backpack = 'solar-bold-backpack'; // Backpack (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case backspace = 'solar-bold-backspace'; // Backspace (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bacteria = 'solar-bold-bacteria'; // Bacteria (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bag = 'solar-bold-bag'; // Bag (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bag2 = 'solar-bold-bag2'; // bag2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bag3 = 'solar-bold-bag3'; // bag3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bag4 = 'solar-bold-bag4'; // bag4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bag5 = 'solar-bold-bag5'; // bag5 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bagCheck = 'solar-bold-bag_check'; // bag_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bagCross = 'solar-bold-bag_cross'; // bag_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bagHeart = 'solar-bold-bag_heart'; // bag_heart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bagMusic = 'solar-bold-bag_music'; // bag_music (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bagMusic2 = 'solar-bold-bag_music2'; // bag_music2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bagSmile = 'solar-bold-bag_smile'; // bag_smile (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case balloon = 'solar-bold-balloon'; // Balloon (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case balls = 'solar-bold-balls'; // Balls (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case banknote = 'solar-bold-banknote'; // Banknote (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case banknote2 = 'solar-bold-banknote2'; // banknote2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case barChair = 'solar-bold-bar_chair'; // bar_chair (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case basketball = 'solar-bold-basketball'; // Basketball (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bath = 'solar-bold-bath'; // Bath (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryCharge = 'solar-bold-battery_charge'; // battery_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryChargeMinimalistic = 'solar-bold-battery_charge_minimalistic'; // battery_charge_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryFull = 'solar-bold-battery_full'; // battery_full (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryFullMinimalistic = 'solar-bold-battery_full_minimalistic'; // battery_full_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryHalf = 'solar-bold-battery_half'; // battery_half (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryHalfMinimalistic = 'solar-bold-battery_half_minimalistic'; // battery_half_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryLow = 'solar-bold-battery_low'; // battery_low (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case batteryLowMinimalistic = 'solar-bold-battery_low_minimalistic'; // battery_low_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bed = 'solar-bold-bed'; // Bed (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bedsideTable = 'solar-bold-bedside_table'; // bedside_table (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bedsideTable2 = 'solar-bold-bedside_table2'; // bedside_table2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bedsideTable3 = 'solar-bold-bedside_table3'; // bedside_table3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bedsideTable4 = 'solar-bold-bedside_table4'; // bedside_table4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bell = 'solar-bold-bell'; // Bell (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bellBing = 'solar-bold-bell_bing'; // bell_bing (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bellOff = 'solar-bold-bell_off'; // bell_off (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case benzeneRing = 'solar-bold-benzene_ring'; // benzene_ring (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bicycling = 'solar-bold-bicycling'; // Bicycling (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bicyclingRound = 'solar-bold-bicycling_round'; // bicycling_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bill = 'solar-bold-bill'; // Bill (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case billCheck = 'solar-bold-bill_check'; // bill_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case billCross = 'solar-bold-bill_cross'; // bill_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case billList = 'solar-bold-bill_list'; // bill_list (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case blackHole = 'solar-bold-black_hole'; // black_hole (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case blackHole2 = 'solar-bold-black_hole2'; // black_hole2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case blackHole3 = 'solar-bold-black_hole3'; // black_hole3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bluetooth = 'solar-bold-bluetooth'; // Bluetooth (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bluetoothCircle = 'solar-bold-bluetooth_circle'; // bluetooth_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bluetoothSquare = 'solar-bold-bluetooth_square'; // bluetooth_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bluetoothWave = 'solar-bold-bluetooth_wave'; // bluetooth_wave (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case body = 'solar-bold-body'; // Body (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bodyShape = 'solar-bold-body_shape'; // body_shape (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bodyShapeMinimalistic = 'solar-bold-body_shape_minimalistic'; // body_shape_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bolt = 'solar-bold-bolt'; // Bolt (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case boltCircle = 'solar-bold-bolt_circle'; // bolt_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bomb = 'solar-bold-bomb'; // Bomb (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bombEmoji = 'solar-bold-bomb_emoji'; // bomb_emoji (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bombMinimalistic = 'solar-bold-bomb_minimalistic'; // bomb_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bone = 'solar-bold-bone'; // Bone (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case boneBroken = 'solar-bold-bone_broken'; // bone_broken (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case boneCrack = 'solar-bold-bone_crack'; // bone_crack (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bones = 'solar-bold-bones'; // Bones (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bonfire = 'solar-bold-bonfire'; // Bonfire (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case book = 'solar-bold-book'; // Book (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case book2 = 'solar-bold-book2'; // book2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookBookmark = 'solar-bold-book_bookmark'; // book_bookmark (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookBookmarkMinimalistic = 'solar-bold-book_bookmark_minimalistic'; // book_bookmark_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookMinimalistic = 'solar-bold-book_minimalistic'; // book_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookmark = 'solar-bold-bookmark'; // Bookmark (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookmarkCircle = 'solar-bold-bookmark_circle'; // bookmark_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookmarkOpened = 'solar-bold-bookmark_opened'; // bookmark_opened (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookmarkSquare = 'solar-bold-bookmark_square'; // bookmark_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bookmarkSquareMinimalistic = 'solar-bold-bookmark_square_minimalistic'; // bookmark_square_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case boombox = 'solar-bold-boombox'; // Boombox (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bottle = 'solar-bold-bottle'; // Bottle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bowling = 'solar-bold-bowling'; // Bowling (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case box = 'solar-bold-box'; // Box (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case boxMinimalistic = 'solar-bold-box_minimalistic'; // box_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case branchingPathsDown = 'solar-bold-branching_paths_down'; // branching_paths_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case branchingPathsUp = 'solar-bold-branching_paths_up'; // branching_paths_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case broom = 'solar-bold-broom'; // Broom (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bug = 'solar-bold-bug'; // Bug (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case bugMinimalistic = 'solar-bold-bug_minimalistic'; // bug_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case buildings = 'solar-bold-buildings'; // Buildings (available in: solar-bold, solar-linear)
    case buildings2 = 'solar-bold-buildings2'; // buildings2 (available in: solar-bold, solar-linear)
    case buildings3 = 'solar-bold-buildings3'; // buildings3 (available in: solar-bold, solar-linear)
    case bus = 'solar-bold-bus'; // Bus (available in: solar-bold, solar-linear)
    case calculator = 'solar-bold-calculator'; // Calculator (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calculatorMinimalistic = 'solar-bold-calculator_minimalistic'; // calculator_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calendar = 'solar-bold-calendar'; // Calendar (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calendarAdd = 'solar-bold-calendar_add'; // calendar_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calendarDate = 'solar-bold-calendar_date'; // calendar_date (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calendarMark = 'solar-bold-calendar_mark'; // calendar_mark (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calendarMinimalistic = 'solar-bold-calendar_minimalistic'; // calendar_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case calendarSearch = 'solar-bold-calendar_search'; // calendar_search (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callCancel = 'solar-bold-call_cancel'; // call_cancel (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callCancelRounded = 'solar-bold-call_cancel_rounded'; // call_cancel_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callChat = 'solar-bold-call_chat'; // call_chat (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callChatRounded = 'solar-bold-call_chat_rounded'; // call_chat_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callDropped = 'solar-bold-call_dropped'; // call_dropped (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callDroppedRounded = 'solar-bold-call_dropped_rounded'; // call_dropped_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callMedicine = 'solar-bold-call_medicine'; // call_medicine (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case callMedicineRounded = 'solar-bold-call_medicine_rounded'; // call_medicine_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case camera = 'solar-bold-camera'; // Camera (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cameraAdd = 'solar-bold-camera_add'; // camera_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cameraMinimalistic = 'solar-bold-camera_minimalistic'; // camera_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cameraRotate = 'solar-bold-camera_rotate'; // camera_rotate (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cameraSquare = 'solar-bold-camera_square'; // camera_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case card = 'solar-bold-card'; // Card (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case card2 = 'solar-bold-card2'; // card2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cardRecive = 'solar-bold-card_recive'; // card_recive (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cardSearch = 'solar-bold-card_search'; // card_search (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cardSend = 'solar-bold-card_send'; // card_send (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cardTransfer = 'solar-bold-card_transfer'; // card_transfer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cardholder = 'solar-bold-cardholder'; // Cardholder (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cart = 'solar-bold-cart'; // Cart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cart2 = 'solar-bold-cart2'; // cart2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cart3 = 'solar-bold-cart3'; // cart3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cart4 = 'solar-bold-cart4'; // cart4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cart5 = 'solar-bold-cart5'; // cart5 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartCheck = 'solar-bold-cart_check'; // cart_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartCross = 'solar-bold-cart_cross'; // cart_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartLarge = 'solar-bold-cart_large'; // cart_large (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartLarge2 = 'solar-bold-cart_large2'; // cart_large2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartLarge3 = 'solar-bold-cart_large3'; // cart_large3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartLarge4 = 'solar-bold-cart_large4'; // cart_large4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartLargeMinimalistic = 'solar-bold-cart_large_minimalistic'; // cart_large_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cartPlus = 'solar-bold-cart_plus'; // cart_plus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case case = 'solar-bold-case'; // Case (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case caseMinimalistic = 'solar-bold-case_minimalistic'; // case_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case caseRound = 'solar-bold-case_round'; // case_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case caseRoundMinimalistic = 'solar-bold-case_round_minimalistic'; // case_round_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cashOut = 'solar-bold-cash_out'; // cash_out (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cassette = 'solar-bold-cassette'; // Cassette (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cassette2 = 'solar-bold-cassette2'; // cassette2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cat = 'solar-bold-cat'; // Cat (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chair = 'solar-bold-chair'; // Chair (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chair2 = 'solar-bold-chair2'; // chair2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chandelier = 'solar-bold-chandelier'; // Chandelier (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chart = 'solar-bold-chart'; // Chart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chart2 = 'solar-bold-chart2'; // chart2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chartSquare = 'solar-bold-chart_square'; // chart_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatDots = 'solar-bold-chat_dots'; // chat_dots (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatLine = 'solar-bold-chat_line'; // chat_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRound = 'solar-bold-chat_round'; // chat_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundCall = 'solar-bold-chat_round_call'; // chat_round_call (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundCheck = 'solar-bold-chat_round_check'; // chat_round_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundDots = 'solar-bold-chat_round_dots'; // chat_round_dots (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundLike = 'solar-bold-chat_round_like'; // chat_round_like (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundLine = 'solar-bold-chat_round_line'; // chat_round_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundMoney = 'solar-bold-chat_round_money'; // chat_round_money (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundUnread = 'solar-bold-chat_round_unread'; // chat_round_unread (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatRoundVideo = 'solar-bold-chat_round_video'; // chat_round_video (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquare = 'solar-bold-chat_square'; // chat_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquare2 = 'solar-bold-chat_square2'; // chat_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquareArrow = 'solar-bold-chat_square_arrow'; // chat_square_arrow (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquareCall = 'solar-bold-chat_square_call'; // chat_square_call (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquareCheck = 'solar-bold-chat_square_check'; // chat_square_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquareCode = 'solar-bold-chat_square_code'; // chat_square_code (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatSquareLike = 'solar-bold-chat_square_like'; // chat_square_like (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chatUnread = 'solar-bold-chat_unread'; // chat_unread (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case checkCircle = 'solar-bold-check_circle'; // check_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case checkRead = 'solar-bold-check_read'; // check_read (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case checkSquare = 'solar-bold-check_square'; // check_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case checklist = 'solar-bold-checklist'; // Checklist (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case checklistMinimalistic = 'solar-bold-checklist_minimalistic'; // checklist_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chefHat = 'solar-bold-chef_hat'; // chef_hat (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chefHatHeart = 'solar-bold-chef_hat_heart'; // chef_hat_heart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case chefHatMinimalistic = 'solar-bold-chef_hat_minimalistic'; // chef_hat_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case circleBottomDown = 'solar-bold-circle_bottom_down'; // circle_bottom_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case circleBottomUp = 'solar-bold-circle_bottom_up'; // circle_bottom_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case circleTopDown = 'solar-bold-circle_top_down'; // circle_top_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case circleTopUp = 'solar-bold-circle_top_up'; // circle_top_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case city = 'solar-bold-city'; // City (available in: solar-bold, solar-linear)
    case clapperboard = 'solar-bold-clapperboard'; // Clapperboard (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clapperboardEdit = 'solar-bold-clapperboard_edit'; // clapperboard_edit (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clapperboardOpen = 'solar-bold-clapperboard_open'; // clapperboard_open (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clapperboardOpenPlay = 'solar-bold-clapperboard_open_play'; // clapperboard_open_play (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clapperboardPlay = 'solar-bold-clapperboard_play'; // clapperboard_play (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clapperboardText = 'solar-bold-clapperboard_text'; // clapperboard_text (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboard = 'solar-bold-clipboard'; // Clipboard (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboardAdd = 'solar-bold-clipboard_add'; // clipboard_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboardCheck = 'solar-bold-clipboard_check'; // clipboard_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboardHeart = 'solar-bold-clipboard_heart'; // clipboard_heart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboardList = 'solar-bold-clipboard_list'; // clipboard_list (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboardRemove = 'solar-bold-clipboard_remove'; // clipboard_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clipboardText = 'solar-bold-clipboard_text'; // clipboard_text (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clockCircle = 'solar-bold-clock_circle'; // clock_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clockSquare = 'solar-bold-clock_square'; // clock_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case closeCircle = 'solar-bold-close_circle'; // close_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case closeSquare = 'solar-bold-close_square'; // close_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case closet = 'solar-bold-closet'; // Closet (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case closet2 = 'solar-bold-closet2'; // closet2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloud = 'solar-bold-cloud'; // Cloud (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudBolt = 'solar-bold-cloud_bolt'; // cloud_bolt (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudBoltMinimalistic = 'solar-bold-cloud_bolt_minimalistic'; // cloud_bolt_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudCheck = 'solar-bold-cloud_check'; // cloud_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudDownload = 'solar-bold-cloud_download'; // cloud_download (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudFile = 'solar-bold-cloud_file'; // cloud_file (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudMinus = 'solar-bold-cloud_minus'; // cloud_minus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudPlus = 'solar-bold-cloud_plus'; // cloud_plus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudRain = 'solar-bold-cloud_rain'; // cloud_rain (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudSnowfall = 'solar-bold-cloud_snowfall'; // cloud_snowfall (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudSnowfallMinimalistic = 'solar-bold-cloud_snowfall_minimalistic'; // cloud_snowfall_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudStorage = 'solar-bold-cloud_storage'; // cloud_storage (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudStorm = 'solar-bold-cloud_storm'; // cloud_storm (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudSun = 'solar-bold-cloud_sun'; // cloud_sun (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudSun2 = 'solar-bold-cloud_sun2'; // cloud_sun2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudUpload = 'solar-bold-cloud_upload'; // cloud_upload (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudWaterdrop = 'solar-bold-cloud_waterdrop'; // cloud_waterdrop (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudWaterdrops = 'solar-bold-cloud_waterdrops'; // cloud_waterdrops (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case clouds = 'solar-bold-clouds'; // Clouds (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloudyMoon = 'solar-bold-cloudy_moon'; // cloudy_moon (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cloundCross = 'solar-bold-clound_cross'; // clound_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case code = 'solar-bold-code'; // Code (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case code2 = 'solar-bold-code2'; // code2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case codeCircle = 'solar-bold-code_circle'; // code_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case codeFile = 'solar-bold-code_file'; // code_file (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case codeScan = 'solar-bold-code_scan'; // code_scan (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case codeSquare = 'solar-bold-code_square'; // code_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case colourTuneing = 'solar-bold-colour_tuneing'; // colour_tuneing (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case command = 'solar-bold-command'; // Command (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case compass = 'solar-bold-compass'; // Compass (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case compassBig = 'solar-bold-compass_big'; // compass_big (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case compassSquare = 'solar-bold-compass_square'; // compass_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case condicioner = 'solar-bold-condicioner'; // Condicioner (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case condicioner2 = 'solar-bold-condicioner2'; // condicioner2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case confetti = 'solar-bold-confetti'; // Confetti (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case confettiMinimalistic = 'solar-bold-confetti_minimalistic'; // confetti_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case confoundedCircle = 'solar-bold-confounded_circle'; // confounded_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case confoundedSquare = 'solar-bold-confounded_square'; // confounded_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case copy = 'solar-bold-copy'; // Copy (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case copyright = 'solar-bold-copyright'; // Copyright (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case corkscrew = 'solar-bold-corkscrew'; // Corkscrew (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cosmetic = 'solar-bold-cosmetic'; // Cosmetic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case courseDown = 'solar-bold-course_down'; // course_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case courseUp = 'solar-bold-course_up'; // course_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cpu = 'solar-bold-cpu'; // CPU (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cpuBolt = 'solar-bold-cpu_bolt'; // cpu_bolt (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case creativeCommons = 'solar-bold-creative_commons'; // creative_commons (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case crop = 'solar-bold-crop'; // Crop (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cropMinimalistic = 'solar-bold-crop_minimalistic'; // crop_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case crown = 'solar-bold-crown'; // Crown (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case crownLine = 'solar-bold-crown_line'; // crown_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case crownMinimalistic = 'solar-bold-crown_minimalistic'; // crown_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case crownStar = 'solar-bold-crown_star'; // crown_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cup = 'solar-bold-cup'; // Cup (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cupFirst = 'solar-bold-cup_first'; // cup_first (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cupHot = 'solar-bold-cup_hot'; // cup_hot (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cupMusic = 'solar-bold-cup_music'; // cup_music (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cupPaper = 'solar-bold-cup_paper'; // cup_paper (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cupStar = 'solar-bold-cup_star'; // cup_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cursor = 'solar-bold-cursor'; // Cursor (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case cursorSquare = 'solar-bold-cursor_square'; // cursor_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case danger = 'solar-bold-danger'; // Danger (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dangerCircle = 'solar-bold-danger_circle'; // danger_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dangerSquare = 'solar-bold-danger_square'; // danger_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dangerTriangle = 'solar-bold-danger_triangle'; // danger_triangle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case database = 'solar-bold-database'; // Database (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case delivery = 'solar-bold-delivery'; // Delivery (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case devices = 'solar-bold-devices'; // Devices (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case diagramDown = 'solar-bold-diagram_down'; // diagram_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case diagramUp = 'solar-bold-diagram_up'; // diagram_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dialog = 'solar-bold-dialog'; // Dialog (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dialog2 = 'solar-bold-dialog2'; // dialog2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case diploma = 'solar-bold-diploma'; // Diploma (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case diplomaVerified = 'solar-bold-diploma_verified'; // diploma_verified (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case diskette = 'solar-bold-diskette'; // Diskette (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dislike = 'solar-bold-dislike'; // Dislike (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case display = 'solar-bold-display'; // Display (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dna = 'solar-bold-dna'; // DNA (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case document = 'solar-bold-document'; // Document (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case documentAdd = 'solar-bold-document_add'; // document_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case documentMedicine = 'solar-bold-document_medicine'; // document_medicine (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case documentText = 'solar-bold-document_text'; // document_text (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case documents = 'solar-bold-documents'; // Documents (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case documentsMinimalistic = 'solar-bold-documents_minimalistic'; // documents_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dollar = 'solar-bold-dollar'; // Dollar (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dollarMinimalistic = 'solar-bold-dollar_minimalistic'; // dollar_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case donut = 'solar-bold-donut'; // Donut (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case donutBitten = 'solar-bold-donut_bitten'; // donut_bitten (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case doubleAltArrowDown = 'solar-bold-double_alt_arrow_down'; // double_alt_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case doubleAltArrowLeft = 'solar-bold-double_alt_arrow_left'; // double_alt_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case doubleAltArrowRight = 'solar-bold-double_alt_arrow_right'; // double_alt_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case doubleAltArrowUp = 'solar-bold-double_alt_arrow_up'; // double_alt_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case download = 'solar-bold-download'; // Download (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case downloadMinimalistic = 'solar-bold-download_minimalistic'; // download_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case downloadSquare = 'solar-bold-download_square'; // download_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case downloadTwiceSquare = 'solar-bold-download_twice_square'; // download_twice_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dropper = 'solar-bold-dropper'; // Dropper (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dropper2 = 'solar-bold-dropper2'; // dropper2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dropper3 = 'solar-bold-dropper3'; // dropper3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dropperMinimalistic = 'solar-bold-dropper_minimalistic'; // dropper_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dropperMinimalistic2 = 'solar-bold-dropper_minimalistic2'; // dropper_minimalistic2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dumbbell = 'solar-bold-dumbbell'; // Dumbbell (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dumbbellLarge = 'solar-bold-dumbbell_large'; // dumbbell_large (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dumbbellLargeMinimalistic = 'solar-bold-dumbbell_large_minimalistic'; // dumbbell_large_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dumbbellSmall = 'solar-bold-dumbbell_small'; // dumbbell_small (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dumbbells = 'solar-bold-dumbbells'; // Dumbbells (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case dumbbells2 = 'solar-bold-dumbbells2'; // dumbbells2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case earth = 'solar-bold-earth'; // Earth (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case electricRefueling = 'solar-bold-electric_refueling'; // electric_refueling (available in: solar-bold, solar-linear)
    case emojiFunnyCircle = 'solar-bold-emoji_funny_circle'; // emoji_funny_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case emojiFunnySquare = 'solar-bold-emoji_funny_square'; // emoji_funny_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case endCall = 'solar-bold-end_call'; // end_call (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case endCallRounded = 'solar-bold-end_call_rounded'; // end_call_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case eraser = 'solar-bold-eraser'; // Eraser (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case eraserCircle = 'solar-bold-eraser_circle'; // eraser_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case eraserSquare = 'solar-bold-eraser_square'; // eraser_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case euro = 'solar-bold-euro'; // Euro (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case exit = 'solar-bold-exit'; // Exit (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case explicit = 'solar-bold-explicit'; // Explicit (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case export = 'solar-bold-export'; // Export (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case expressionlessCircle = 'solar-bold-expressionless_circle'; // expressionless_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case expressionlessSquare = 'solar-bold-expressionless_square'; // expressionless_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case eye = 'solar-bold-eye'; // Eye (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case eyeClosed = 'solar-bold-eye_closed'; // eye_closed (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case eyeScan = 'solar-bold-eye_scan'; // eye_scan (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case faceScanCircle = 'solar-bold-face_scan_circle'; // face_scan_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case faceScanSquare = 'solar-bold-face_scan_square'; // face_scan_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case facemaskCircle = 'solar-bold-facemask_circle'; // facemask_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case facemaskSquare = 'solar-bold-facemask_square'; // facemask_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case feed = 'solar-bold-feed'; // Feed (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ferrisWheel = 'solar-bold-ferris_wheel'; // ferris_wheel (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case figma = 'solar-bold-figma'; // Figma (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case figmaFile = 'solar-bold-figma_file'; // figma_file (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case file = 'solar-bold-file'; // File (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileCheck = 'solar-bold-file_check'; // file_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileCorrupted = 'solar-bold-file_corrupted'; // file_corrupted (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileDownload = 'solar-bold-file_download'; // file_download (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileFavourite = 'solar-bold-file_favourite'; // file_favourite (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileLeft = 'solar-bold-file_left'; // file_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileRemove = 'solar-bold-file_remove'; // file_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileRight = 'solar-bold-file_right'; // file_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileSend = 'solar-bold-file_send'; // file_send (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileSmile = 'solar-bold-file_smile_)'; // file_smile_) (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fileText = 'solar-bold-file_text'; // file_text (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case filter = 'solar-bold-filter'; // Filter (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case filters = 'solar-bold-filters'; // Filters (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fire = 'solar-bold-fire'; // Fire (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fireMinimalistic = 'solar-bold-fire_minimalistic'; // fire_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fireSquare = 'solar-bold-fire_square'; // fire_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flag = 'solar-bold-flag'; // Flag (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flag2 = 'solar-bold-flag2'; // flag2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flame = 'solar-bold-flame'; // Flame (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flashDrive = 'solar-bold-flash_drive'; // flash_drive (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flashlight = 'solar-bold-flashlight'; // Flashlight (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flashlightOn = 'solar-bold-flashlight_on'; // flashlight_on (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flipHorizontal = 'solar-bold-flip_horizontal'; // flip_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case flipVertical = 'solar-bold-flip_vertical'; // flip_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case floorLamp = 'solar-bold-floor_lamp'; // floor_lamp (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case floorLampMinimalistic = 'solar-bold-floor_lamp_minimalistic'; // floor_lamp_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fog = 'solar-bold-fog'; // Fog (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folder = 'solar-bold-folder'; // Folder (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folder2 = 'solar-bold-folder2'; // folder2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderCheck = 'solar-bold-folder_check'; // folder_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderCloud = 'solar-bold-folder_cloud'; // folder_cloud (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderError = 'solar-bold-folder_error'; // folder_error (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderFavouritebookmark = 'solar-bold-folder_favourite(bookmark)'; // folder_favourite(bookmark) (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderFavouritestar = 'solar-bold-folder_favourite(star)'; // folder_favourite(star) (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderOpen = 'solar-bold-folder_open'; // folder_open (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderPathConnect = 'solar-bold-folder_path_connect'; // folder_path_connect (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderSecurity = 'solar-bold-folder_security'; // folder_security (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case folderWithFiles = 'solar-bold-folder_with_files'; // folder_with_files (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case football = 'solar-bold-football'; // Football (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case forbidden = 'solar-bold-forbidden'; // Forbidden (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case forbiddenCircle = 'solar-bold-forbidden_circle'; // forbidden_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case forward = 'solar-bold-forward'; // Forward (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case forward2 = 'solar-bold-forward2'; // forward2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fridge = 'solar-bold-fridge'; // Fridge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fuel = 'solar-bold-fuel'; // Fuel (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fullScreen = 'solar-bold-full_screen'; // full_screen (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fullScreenCircle = 'solar-bold-full_screen_circle'; // full_screen_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case fullScreenSquare = 'solar-bold-full_screen_square'; // full_screen_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gallery = 'solar-bold-gallery'; // Gallery (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryAdd = 'solar-bold-gallery_add'; // gallery_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryCheck = 'solar-bold-gallery_check'; // gallery_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryCircle = 'solar-bold-gallery_circle'; // gallery_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryDownload = 'solar-bold-gallery_download'; // gallery_download (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryEdit = 'solar-bold-gallery_edit'; // gallery_edit (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryFavourite = 'solar-bold-gallery_favourite'; // gallery_favourite (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryMinimalistic = 'solar-bold-gallery_minimalistic'; // gallery_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryRemove = 'solar-bold-gallery_remove'; // gallery_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryRound = 'solar-bold-gallery_round'; // gallery_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gallerySend = 'solar-bold-gallery_send'; // gallery_send (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case galleryWide = 'solar-bold-gallery_wide'; // gallery_wide (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gameboy = 'solar-bold-gameboy'; // Gameboy (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gamepad = 'solar-bold-gamepad'; // Gamepad (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gamepadCharge = 'solar-bold-gamepad_charge'; // gamepad_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gamepadMinimalistic = 'solar-bold-gamepad_minimalistic'; // gamepad_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gamepadNoCharge = 'solar-bold-gamepad_no_charge'; // gamepad_no_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gamepadOld = 'solar-bold-gamepad_old'; // gamepad_old (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case garage = 'solar-bold-garage'; // Garage (available in: solar-bold, solar-linear)
    case gasStation = 'solar-bold-gas_station'; // gas_station (available in: solar-bold, solar-linear)
    case ghost = 'solar-bold-ghost'; // Ghost (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ghostSmile = 'solar-bold-ghost_smile'; // ghost_smile (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gift = 'solar-bold-gift'; // Gift (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case glasses = 'solar-bold-glasses'; // Glasses (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case global = 'solar-bold-global'; // Global (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case globus = 'solar-bold-globus'; // Globus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case golf = 'solar-bold-golf'; // Golf (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case gps = 'solar-bold-gps'; // GPS (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case graph = 'solar-bold-graph'; // Graph (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case graphDown = 'solar-bold-graph_down'; // graph_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case graphDownNew = 'solar-bold-graph_down_new'; // graph_down_new (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case graphNew = 'solar-bold-graph_new'; // graph_new (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case graphNewUp = 'solar-bold-graph_new_up'; // graph_new_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case graphUp = 'solar-bold-graph_up'; // graph_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hamburgerMenu = 'solar-bold-hamburger_menu'; // hamburger_menu (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case handHeart = 'solar-bold-hand_heart'; // hand_heart (available in: solar-bold, solar-linear)
    case handMoney = 'solar-bold-hand_money'; // hand_money (available in: solar-bold, solar-linear)
    case handPills = 'solar-bold-hand_pills'; // hand_pills (available in: solar-bold, solar-linear)
    case handShake = 'solar-bold-hand_shake'; // hand_shake (available in: solar-bold, solar-linear)
    case handStars = 'solar-bold-hand_stars'; // hand_stars (available in: solar-bold, solar-linear)
    case hanger = 'solar-bold-hanger'; // Hanger (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hanger2 = 'solar-bold-hanger2'; // hanger2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hashtag = 'solar-bold-hashtag'; // Hashtag (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hashtagChat = 'solar-bold-hashtag_chat'; // hashtag_chat (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hashtagCircle = 'solar-bold-hashtag_circle'; // hashtag_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hashtagSquare = 'solar-bold-hashtag_square'; // hashtag_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case headphonesRound = 'solar-bold-headphones_round'; // headphones_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case headphonesRoundSound = 'solar-bold-headphones_round_sound'; // headphones_round_sound (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case headphonesSquare = 'solar-bold-headphones_square'; // headphones_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case headphonesSquareSound = 'solar-bold-headphones_square_sound'; // headphones_square_sound (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case health = 'solar-bold-health'; // Health (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heart = 'solar-bold-heart'; // Heart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartAngle = 'solar-bold-heart_angle'; // heart_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartBroken = 'solar-bold-heart_broken'; // heart_broken (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartLock = 'solar-bold-heart_lock'; // heart_lock (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartPulse = 'solar-bold-heart_pulse'; // heart_pulse (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartPulse2 = 'solar-bold-heart_pulse2'; // heart_pulse2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartShine = 'solar-bold-heart_shine'; // heart_shine (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case heartUnlock = 'solar-bold-heart_unlock'; // heart_unlock (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hearts = 'solar-bold-hearts'; // Hearts (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case help = 'solar-bold-help'; // Help (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case highDefinition = 'solar-bold-high_definition'; // high_definition (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case highQuality = 'solar-bold-high_quality'; // high_quality (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hiking = 'solar-bold-hiking'; // Hiking (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hikingMinimalistic = 'solar-bold-hiking_minimalistic'; // hiking_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hikingRound = 'solar-bold-hiking_round'; // hiking_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case history = 'solar-bold-history'; // History (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case history2 = 'solar-bold-history2'; // history2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case history3 = 'solar-bold-history3'; // history3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case home = 'solar-bold-home'; // Home (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case home2 = 'solar-bold-home2'; // home2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeAdd = 'solar-bold-home_add'; // home_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeAddAngle = 'solar-bold-home_add_angle'; // home_add_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeAngle = 'solar-bold-home_angle'; // home_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeAngle2 = 'solar-bold-home_angle2'; // home_angle2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeSmile = 'solar-bold-home_smile'; // home_smile (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeSmileAngle = 'solar-bold-home_smile_angle'; // home_smile_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeWifi = 'solar-bold-home_wifi'; // home_wifi (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case homeWifiAngle = 'solar-bold-home_wifi_angle'; // home_wifi_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hospital = 'solar-bold-hospital'; // Hospital (available in: solar-bold, solar-linear)
    case hourglass = 'solar-bold-hourglass'; // Hourglass (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case hourglassLine = 'solar-bold-hourglass_line'; // hourglass_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case iPhone = 'solar-bold-i_phone'; // i_phone (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case icon4K = 'solar-bold-4_k'; // 4_k (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case import = 'solar-bold-import'; // Import (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case inbox = 'solar-bold-inbox'; // Inbox (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case inboxArchive = 'solar-bold-inbox_archive'; // inbox_archive (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case inboxIn = 'solar-bold-inbox_in'; // inbox_in (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case inboxLine = 'solar-bold-inbox_line'; // inbox_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case inboxOut = 'solar-bold-inbox_out'; // inbox_out (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case inboxUnread = 'solar-bold-inbox_unread'; // inbox_unread (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case incognito = 'solar-bold-incognito'; // Incognito (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case incomingCall = 'solar-bold-incoming_call'; // incoming_call (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case incomingCallRounded = 'solar-bold-incoming_call_rounded'; // incoming_call_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case infinity = 'solar-bold-infinity'; // Infinity (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case infoCircle = 'solar-bold-info_circle'; // info_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case infoSquare = 'solar-bold-info_square'; // info_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case jarOfPills = 'solar-bold-jar_of_pills'; // jar_of_pills (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case jarOfPills2 = 'solar-bold-jar_of_pills2'; // jar_of_pills2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case key = 'solar-bold-key'; // Key (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keyMinimalistic = 'solar-bold-key_minimalistic'; // key_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keyMinimalistic2 = 'solar-bold-key_minimalistic2'; // key_minimalistic2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keyMinimalisticSquare = 'solar-bold-key_minimalistic_square'; // key_minimalistic_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keyMinimalisticSquare2 = 'solar-bold-key_minimalistic_square2'; // key_minimalistic_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keyMinimalisticSquare3 = 'solar-bold-key_minimalistic_square3'; // key_minimalistic_square3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keySquare = 'solar-bold-key_square'; // key_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keySquare2 = 'solar-bold-key_square2'; // key_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case keyboard = 'solar-bold-keyboard'; // Keyboard (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case kickScooter = 'solar-bold-kick_scooter'; // kick_scooter (available in: solar-bold, solar-linear)
    case ladle = 'solar-bold-ladle'; // Ladle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lamp = 'solar-bold-lamp'; // Lamp (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case laptop = 'solar-bold-laptop'; // Laptop (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case laptop2 = 'solar-bold-laptop2'; // laptop2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case laptop3 = 'solar-bold-laptop3'; // laptop3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case laptopMinimalistic = 'solar-bold-laptop_minimalistic'; // laptop_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case layers = 'solar-bold-layers'; // Layers (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case layersMinimalistic = 'solar-bold-layers_minimalistic'; // layers_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case leaf = 'solar-bold-leaf'; // Leaf (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case letter = 'solar-bold-letter'; // Letter (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case letterOpened = 'solar-bold-letter_opened'; // letter_opened (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case letterUnread = 'solar-bold-letter_unread'; // letter_unread (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case library = 'solar-bold-library'; // Library (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lightbulb = 'solar-bold-lightbulb'; // Lightbulb (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lightbulbBolt = 'solar-bold-lightbulb_bolt'; // lightbulb_bolt (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lightbulbMinimalistic = 'solar-bold-lightbulb_minimalistic'; // lightbulb_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lightning = 'solar-bold-lightning'; // Lightning (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case like = 'solar-bold-like'; // Like (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case link = 'solar-bold-link'; // Link (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkBroken = 'solar-bold-link_broken'; // link_broken (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkBrokenMinimalistic = 'solar-bold-link_broken_minimalistic'; // link_broken_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkCircle = 'solar-bold-link_circle'; // link_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkMinimalistic = 'solar-bold-link_minimalistic'; // link_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkMinimalistic2 = 'solar-bold-link_minimalistic2'; // link_minimalistic2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkRound = 'solar-bold-link_round'; // link_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkRoundAngle = 'solar-bold-link_round_angle'; // link_round_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case linkSquare = 'solar-bold-link_square'; // link_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case list = 'solar-bold-list'; // List (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case list1 = 'solar-bold-list1'; // list1 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listArrowDown = 'solar-bold-list_arrow_down'; // list_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listArrowDownMinimalistic = 'solar-bold-list_arrow_down_minimalistic'; // list_arrow_down_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listArrowUp = 'solar-bold-list_arrow_up'; // list_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listArrowUpMinimalistic = 'solar-bold-list_arrow_up_minimalistic'; // list_arrow_up_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listCheck = 'solar-bold-list_check'; // list_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listCheckMinimalistic = 'solar-bold-list_check_minimalistic'; // list_check_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listCross = 'solar-bold-list_cross'; // list_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listCrossMinimalistic = 'solar-bold-list_cross_minimalistic'; // list_cross_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listDown = 'solar-bold-list_down'; // list_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listDownMinimalistic = 'solar-bold-list_down_minimalistic'; // list_down_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listHeart = 'solar-bold-list_heart'; // list_heart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listHeartMinimalistic = 'solar-bold-list_heart_minimalistic'; // list_heart_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listUp = 'solar-bold-list_up'; // list_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case listUpMinimalistic = 'solar-bold-list_up_minimalistic'; // list_up_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lock = 'solar-bold-lock'; // Lock (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockKeyhole = 'solar-bold-lock_keyhole'; // lock_keyhole (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockKeyholeMinimalistic = 'solar-bold-lock_keyhole_minimalistic'; // lock_keyhole_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockKeyholeMinimalisticUnlocked = 'solar-bold-lock_keyhole_minimalistic_unlocked'; // lock_keyhole_minimalistic_unlocked (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockKeyholeUnlocked = 'solar-bold-lock_keyhole_unlocked'; // lock_keyhole_unlocked (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockPassword = 'solar-bold-lock_password'; // lock_password (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockPasswordUnlocked = 'solar-bold-lock_password_unlocked'; // lock_password_unlocked (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case lockUnlocked = 'solar-bold-lock_unlocked'; // lock_unlocked (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case login = 'solar-bold-login'; // Login (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case login2 = 'solar-bold-login2'; // login2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case login3 = 'solar-bold-login3'; // login3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case logout = 'solar-bold-logout'; // Logout (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case logout2 = 'solar-bold-logout2'; // logout2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case logout3 = 'solar-bold-logout3'; // logout3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magicStick = 'solar-bold-magic_stick'; // magic_stick (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magicStick2 = 'solar-bold-magic_stick2'; // magic_stick2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magicStick3 = 'solar-bold-magic_stick3'; // magic_stick3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magnet = 'solar-bold-magnet'; // Magnet (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magnetWave = 'solar-bold-magnet_wave'; // magnet_wave (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magnifer = 'solar-bold-magnifer'; // Magnifer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magniferBug = 'solar-bold-magnifer_bug'; // magnifer_bug (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magniferZoomIn = 'solar-bold-magnifer_zoom_in'; // magnifer_zoom_in (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case magniferZoomOut = 'solar-bold-magnifer_zoom_out'; // magnifer_zoom_out (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mailbox = 'solar-bold-mailbox'; // Mailbox (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case map = 'solar-bold-map'; // Map (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapArrowDown = 'solar-bold-map_arrow_down'; // map_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapArrowLeft = 'solar-bold-map_arrow_left'; // map_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapArrowRight = 'solar-bold-map_arrow_right'; // map_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapArrowSquare = 'solar-bold-map_arrow_square'; // map_arrow_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapArrowUp = 'solar-bold-map_arrow_up'; // map_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPoint = 'solar-bold-map_point'; // map_point (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointAdd = 'solar-bold-map_point_add'; // map_point_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointFavourite = 'solar-bold-map_point_favourite'; // map_point_favourite (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointHospital = 'solar-bold-map_point_hospital'; // map_point_hospital (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointRemove = 'solar-bold-map_point_remove'; // map_point_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointRotate = 'solar-bold-map_point_rotate'; // map_point_rotate (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointSchool = 'solar-bold-map_point_school'; // map_point_school (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointSearch = 'solar-bold-map_point_search'; // map_point_search (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mapPointWave = 'solar-bold-map_point_wave'; // map_point_wave (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maskHapply = 'solar-bold-mask_happly'; // mask_happly (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maskSad = 'solar-bold-mask_sad'; // mask_sad (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case masks = 'solar-bold-masks'; // Masks (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maximize = 'solar-bold-maximize'; // Maximize (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maximizeSquare = 'solar-bold-maximize_square'; // maximize_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maximizeSquare2 = 'solar-bold-maximize_square2'; // maximize_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maximizeSquare3 = 'solar-bold-maximize_square3'; // maximize_square3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case maximizeSquareMinimalistic = 'solar-bold-maximize_square_minimalistic'; // maximize_square_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medalRibbon = 'solar-bold-medal_ribbon'; // medal_ribbon (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medalRibbonStar = 'solar-bold-medal_ribbon_star'; // medal_ribbon_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medalRibbonsStar = 'solar-bold-medal_ribbons_star'; // medal_ribbons_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medalStar = 'solar-bold-medal_star'; // medal_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medalStarCircle = 'solar-bold-medal_star_circle'; // medal_star_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medalStarSquare = 'solar-bold-medal_star_square'; // medal_star_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case medicalKit = 'solar-bold-medical_kit'; // medical_kit (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case meditation = 'solar-bold-meditation'; // Meditation (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case meditationRound = 'solar-bold-meditation_round'; // meditation_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case men = 'solar-bold-men'; // Men (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mentionCircle = 'solar-bold-mention_circle'; // mention_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mentionSquare = 'solar-bold-mention_square'; // mention_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case menuDots = 'solar-bold-menu_dots'; // menu_dots (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case menuDotsCircle = 'solar-bold-menu_dots_circle'; // menu_dots_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case menuDotsSquare = 'solar-bold-menu_dots_square'; // menu_dots_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case microphone = 'solar-bold-microphone'; // Microphone (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case microphone2 = 'solar-bold-microphone2'; // microphone2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case microphone3 = 'solar-bold-microphone3'; // microphone3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case microphoneLarge = 'solar-bold-microphone_large'; // microphone_large (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimalisticMagnifer = 'solar-bold-minimalistic_magnifer'; // minimalistic_magnifer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimalisticMagniferBug = 'solar-bold-minimalistic_magnifer_bug'; // minimalistic_magnifer_bug (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimalisticMagniferZoomIn = 'solar-bold-minimalistic_magnifer_zoom_in'; // minimalistic_magnifer_zoom_in (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimalisticMagniferZoomOut = 'solar-bold-minimalistic_magnifer_zoom_out'; // minimalistic_magnifer_zoom_out (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimize = 'solar-bold-minimize'; // Minimize (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimizeSquare = 'solar-bold-minimize_square'; // minimize_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimizeSquare2 = 'solar-bold-minimize_square2'; // minimize_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimizeSquare3 = 'solar-bold-minimize_square3'; // minimize_square3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minimizeSquareMinimalistic = 'solar-bold-minimize_square_minimalistic'; // minimize_square_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minusCircle = 'solar-bold-minus_circle'; // minus_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case minusSquare = 'solar-bold-minus_square'; // minus_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mirror = 'solar-bold-mirror'; // Mirror (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mirrorLeft = 'solar-bold-mirror_left'; // mirror_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mirrorRight = 'solar-bold-mirror_right'; // mirror_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case moneyBag = 'solar-bold-money_bag'; // money_bag (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case monitor = 'solar-bold-monitor'; // Monitor (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case monitorCamera = 'solar-bold-monitor_camera'; // monitor_camera (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case monitorSmartphone = 'solar-bold-monitor_smartphone'; // monitor_smartphone (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case moon = 'solar-bold-moon'; // Moon (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case moonFog = 'solar-bold-moon_fog'; // moon_fog (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case moonSleep = 'solar-bold-moon_sleep'; // moon_sleep (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case moonStars = 'solar-bold-moon_stars'; // moon_stars (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mouse = 'solar-bold-mouse'; // Mouse (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mouseCircle = 'solar-bold-mouse_circle'; // mouse_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case mouseMinimalistic = 'solar-bold-mouse_minimalistic'; // mouse_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case moveToFolder = 'solar-bold-move_to_folder'; // move_to_folder (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case multipleForwardLeft = 'solar-bold-multiple_forward_left'; // multiple_forward_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case multipleForwardRight = 'solar-bold-multiple_forward_right'; // multiple_forward_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicLibrary = 'solar-bold-music_library'; // music_library (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicLibrary2 = 'solar-bold-music_library2'; // music_library2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNote = 'solar-bold-music_note'; // music_note (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNote2 = 'solar-bold-music_note2'; // music_note2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNote3 = 'solar-bold-music_note3'; // music_note3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNote4 = 'solar-bold-music_note4'; // music_note4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNoteSlider = 'solar-bold-music_note_slider'; // music_note_slider (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNoteSlider2 = 'solar-bold-music_note_slider2'; // music_note_slider2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case musicNotes = 'solar-bold-music_notes'; // music_notes (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case muted = 'solar-bold-muted'; // Muted (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notebook = 'solar-bold-notebook'; // Notebook (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notebookBookmark = 'solar-bold-notebook_bookmark'; // notebook_bookmark (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notebookMinimalistic = 'solar-bold-notebook_minimalistic'; // notebook_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notebookSquare = 'solar-bold-notebook_square'; // notebook_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notes = 'solar-bold-notes'; // Notes (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notesMinimalistic = 'solar-bold-notes_minimalistic'; // notes_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notificationLinesRemove = 'solar-bold-notification_lines_remove'; // notification_lines_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notificationRemove = 'solar-bold-notification_remove'; // notification_remove (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notificationUnread = 'solar-bold-notification_unread'; // notification_unread (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case notificationUnreadLines = 'solar-bold-notification_unread_lines'; // notification_unread_lines (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case objectScan = 'solar-bold-object_scan'; // object_scan (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case outgoingCall = 'solar-bold-outgoing_call'; // outgoing_call (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case outgoingCallRounded = 'solar-bold-outgoing_call_rounded'; // outgoing_call_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ovenMitts = 'solar-bold-oven_mitts'; // oven_mitts (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ovenMittsMinimalistic = 'solar-bold-oven_mitts_minimalistic'; // oven_mitts_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paintRoller = 'solar-bold-paint_roller'; // paint_roller (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case palette = 'solar-bold-palette'; // Palette (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paletteRound = 'solar-bold-palette_round'; // palette_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pallete2 = 'solar-bold-pallete2'; // pallete2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case panorama = 'solar-bold-panorama'; // Panorama (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paperBin = 'solar-bold-paper_bin'; // paper_bin (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paperclip = 'solar-bold-paperclip'; // Paperclip (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paperclip2 = 'solar-bold-paperclip2'; // paperclip2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paperclipRounded = 'solar-bold-paperclip_rounded'; // paperclip_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paperclipRounded2 = 'solar-bold-paperclip_rounded2'; // paperclip_rounded2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paragraphSpacing = 'solar-bold-paragraph_spacing'; // paragraph_spacing (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case passport = 'solar-bold-passport'; // Passport (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case passportMinimalistic = 'solar-bold-passport_minimalistic'; // passport_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case password = 'solar-bold-password'; // Password (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case passwordMinimalistic = 'solar-bold-password_minimalistic'; // password_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case passwordMinimalisticInput = 'solar-bold-password_minimalistic_input'; // password_minimalistic_input (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pause = 'solar-bold-pause'; // Pause (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pauseCircle = 'solar-bold-pause_circle'; // pause_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case paw = 'solar-bold-paw'; // Paw (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pen = 'solar-bold-pen'; // Pen (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pen2 = 'solar-bold-pen2'; // pen2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case penNewRound = 'solar-bold-pen_new_round'; // pen_new_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case penNewSquare = 'solar-bold-pen_new_square'; // pen_new_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case peopleNearby = 'solar-bold-people_nearby'; // people_nearby (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case perfume = 'solar-bold-perfume'; // Perfume (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case phone = 'solar-bold-phone'; // Phone (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case phoneCalling = 'solar-bold-phone_calling'; // phone_calling (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case phoneCallingRounded = 'solar-bold-phone_calling_rounded'; // phone_calling_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case phoneRounded = 'solar-bold-phone_rounded'; // phone_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pieChart = 'solar-bold-pie_chart'; // pie_chart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pieChart2 = 'solar-bold-pie_chart2'; // pie_chart2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pieChart3 = 'solar-bold-pie_chart3'; // pie_chart3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pill = 'solar-bold-pill'; // Pill (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pills = 'solar-bold-pills'; // Pills (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pills2 = 'solar-bold-pills2'; // pills2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pills3 = 'solar-bold-pills3'; // pills3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pin = 'solar-bold-pin'; // Pin (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pinCircle = 'solar-bold-pin_circle'; // pin_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pinList = 'solar-bold-pin_list'; // pin_list (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pip = 'solar-bold-pip'; // PIP (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pip2 = 'solar-bold-pip2'; // pip2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pipette = 'solar-bold-pipette'; // Pipette (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plaaylistMinimalistic = 'solar-bold-plaaylist_minimalistic'; // plaaylist_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plain = 'solar-bold-plain'; // Plain (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plain2 = 'solar-bold-plain2'; // plain2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plain3 = 'solar-bold-plain3'; // plain3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case planet = 'solar-bold-planet'; // Planet (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case planet2 = 'solar-bold-planet2'; // planet2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case planet3 = 'solar-bold-planet3'; // planet3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case planet4 = 'solar-bold-planet4'; // planet4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plate = 'solar-bold-plate'; // Plate (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case play = 'solar-bold-play'; // Play (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playCircle = 'solar-bold-play_circle'; // play_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playStream = 'solar-bold-play_stream'; // play_stream (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playbackSpeed = 'solar-bold-playback_speed'; // playback_speed (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playlist = 'solar-bold-playlist'; // Playlist (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playlist2 = 'solar-bold-playlist2'; // playlist2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playlistMinimalistic2 = 'solar-bold-playlist_minimalistic2'; // playlist_minimalistic2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case playlistMinimalistic3 = 'solar-bold-playlist_minimalistic3'; // playlist_minimalistic3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plugCircle = 'solar-bold-plug_circle'; // plug_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case plusMinus = 'solar-bold-plus,_minus'; // plus,_minus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case podcast = 'solar-bold-podcast'; // Podcast (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pointOnMap = 'solar-bold-point_on_map'; // point_on_map (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pointOnMapPerspective = 'solar-bold-point_on_map_perspective'; // point_on_map_perspective (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case postsCarouselHorizontal = 'solar-bold-posts_carousel_horizontal'; // posts_carousel_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case postsCarouselVertical = 'solar-bold-posts_carousel_vertical'; // posts_carousel_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case power = 'solar-bold-power'; // Power (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case presentationGraph = 'solar-bold-presentation_graph'; // presentation_graph (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case printer = 'solar-bold-printer'; // Printer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case printer2 = 'solar-bold-printer2'; // printer2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case printerMinimalistic = 'solar-bold-printer_minimalistic'; // printer_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case programming = 'solar-bold-programming'; // Programming (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case projector = 'solar-bold-projector'; // Projector (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pulse = 'solar-bold-pulse'; // Pulse (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case pulse2 = 'solar-bold-pulse2'; // pulse2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case qrCode = 'solar-bold-qr_code'; // qr_code (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case questionCircle = 'solar-bold-question_circle'; // question_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case questionSquare = 'solar-bold-question_square'; // question_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case quitFullScreen = 'solar-bold-quit_full_screen'; // quit_full_screen (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case quitFullScreenCircle = 'solar-bold-quit_full_screen_circle'; // quit_full_screen_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case quitFullScreenSquare = 'solar-bold-quit_full_screen_square'; // quit_full_screen_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case quitPip = 'solar-bold-quit_pip'; // quit_pip (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case radar = 'solar-bold-radar'; // Radar (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case radar2 = 'solar-bold-radar2'; // radar2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case radialBlur = 'solar-bold-radial_blur'; // radial_blur (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case radio = 'solar-bold-radio'; // Radio (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case radioMinimalistic = 'solar-bold-radio_minimalistic'; // radio_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ranking = 'solar-bold-ranking'; // Ranking (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reciveSquare = 'solar-bold-recive_square'; // recive_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reciveTwiceSquare = 'solar-bold-recive_twice_square'; // recive_twice_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case record = 'solar-bold-record'; // Record (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case recordCircle = 'solar-bold-record_circle'; // record_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case recordMinimalistic = 'solar-bold-record_minimalistic'; // record_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case recordSquare = 'solar-bold-record_square'; // record_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reel = 'solar-bold-reel'; // Reel (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reel2 = 'solar-bold-reel2'; // reel2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case refresh = 'solar-bold-refresh'; // Refresh (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case refreshCircle = 'solar-bold-refresh_circle'; // refresh_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case refreshSquare = 'solar-bold-refresh_square'; // refresh_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case remoteController = 'solar-bold-remote_controller'; // remote_controller (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case remoteController2 = 'solar-bold-remote_controller2'; // remote_controller2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case remoteControllerMinimalistic = 'solar-bold-remote_controller_minimalistic'; // remote_controller_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case removeFolder = 'solar-bold-remove_folder'; // remove_folder (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reorder = 'solar-bold-reorder'; // Reorder (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case repeat = 'solar-bold-repeat'; // Repeat (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case repeatOne = 'solar-bold-repeat_one'; // repeat_one (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case repeatOneMinimalistic = 'solar-bold-repeat_one_minimalistic'; // repeat_one_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reply = 'solar-bold-reply'; // Reply (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case reply2 = 'solar-bold-reply2'; // reply2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case restart = 'solar-bold-restart'; // Restart (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case restartCircle = 'solar-bold-restart_circle'; // restart_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case restartSquare = 'solar-bold-restart_square'; // restart_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case revote = 'solar-bold-revote'; // Revote (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewind10SecondsBack = 'solar-bold-rewind10_seconds_back'; // rewind10_seconds_back (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewind10SecondsForward = 'solar-bold-rewind10_seconds_forward'; // rewind10_seconds_forward (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewind15SecondsBack = 'solar-bold-rewind15_seconds_back'; // rewind15_seconds_back (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewind15SecondsForward = 'solar-bold-rewind15_seconds_forward'; // rewind15_seconds_forward (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewind5SecondsBack = 'solar-bold-rewind5_seconds_back'; // rewind5_seconds_back (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewind5SecondsForward = 'solar-bold-rewind5_seconds_forward'; // rewind5_seconds_forward (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewindBack = 'solar-bold-rewind_back'; // rewind_back (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewindBackCircle = 'solar-bold-rewind_back_circle'; // rewind_back_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewindForward = 'solar-bold-rewind_forward'; // rewind_forward (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rewindForwardCircle = 'solar-bold-rewind_forward_circle'; // rewind_forward_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rocket = 'solar-bold-rocket'; // Rocket (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rocket2 = 'solar-bold-rocket2'; // rocket2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rollingPin = 'solar-bold-rolling_pin'; // rolling_pin (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundAltArrowDown = 'solar-bold-round_alt_arrow_down'; // round_alt_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundAltArrowLeft = 'solar-bold-round_alt_arrow_left'; // round_alt_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundAltArrowRight = 'solar-bold-round_alt_arrow_right'; // round_alt_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundAltArrowUp = 'solar-bold-round_alt_arrow_up'; // round_alt_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowDown = 'solar-bold-round_arrow_down'; // round_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowLeft = 'solar-bold-round_arrow_left'; // round_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowLeftDown = 'solar-bold-round_arrow_left_down'; // round_arrow_left_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowLeftUp = 'solar-bold-round_arrow_left_up'; // round_arrow_left_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowRight = 'solar-bold-round_arrow_right'; // round_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowRightDown = 'solar-bold-round_arrow_right_down'; // round_arrow_right_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowRightUp = 'solar-bold-round_arrow_right_up'; // round_arrow_right_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundArrowUp = 'solar-bold-round_arrow_up'; // round_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundDoubleAltArrowDown = 'solar-bold-round_double_alt_arrow_down'; // round_double_alt_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundDoubleAltArrowLeft = 'solar-bold-round_double_alt_arrow_left'; // round_double_alt_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundDoubleAltArrowRight = 'solar-bold-round_double_alt_arrow_right'; // round_double_alt_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundDoubleAltArrowUp = 'solar-bold-round_double_alt_arrow_up'; // round_double_alt_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundGraph = 'solar-bold-round_graph'; // round_graph (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundSortHorizontal = 'solar-bold-round_sort_horizontal'; // round_sort_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundSortVertical = 'solar-bold-round_sort_vertical'; // round_sort_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundTransferDiagonal = 'solar-bold-round_transfer_diagonal'; // round_transfer_diagonal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundTransferHorizontal = 'solar-bold-round_transfer_horizontal'; // round_transfer_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundTransferVertical = 'solar-bold-round_transfer_vertical'; // round_transfer_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundedMagnifer = 'solar-bold-rounded_magnifer'; // rounded_magnifer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundedMagniferBug = 'solar-bold-rounded_magnifer_bug'; // rounded_magnifer_bug (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundedMagniferZoomIn = 'solar-bold-rounded_magnifer_zoom_in'; // rounded_magnifer_zoom_in (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case roundedMagniferZoomOut = 'solar-bold-rounded_magnifer_zoom_out'; // rounded_magnifer_zoom_out (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case route = 'solar-bold-route'; // Route (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case routing = 'solar-bold-routing'; // Routing (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case routing2 = 'solar-bold-routing2'; // routing2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case routing3 = 'solar-bold-routing3'; // routing3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ruble = 'solar-bold-ruble'; // Ruble (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rugby = 'solar-bold-rugby'; // Rugby (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ruler = 'solar-bold-ruler'; // Ruler (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rulerAngular = 'solar-bold-ruler_angular'; // ruler_angular (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rulerCrossPen = 'solar-bold-ruler_cross_pen'; // ruler_cross_pen (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case rulerPen = 'solar-bold-ruler_pen'; // ruler_pen (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case running = 'solar-bold-running'; // Running (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case running2 = 'solar-bold-running2'; // running2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case runningRound = 'solar-bold-running_round'; // running_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sadCircle = 'solar-bold-sad_circle'; // sad_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sadSquare = 'solar-bold-sad_square'; // sad_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case safe2 = 'solar-bold-safe2'; // safe2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case safeCircle = 'solar-bold-safe_circle'; // safe_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case safeSquare = 'solar-bold-safe_square'; // safe_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sale = 'solar-bold-sale'; // Sale (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case saleSquare = 'solar-bold-sale_square'; // sale_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case satellite = 'solar-bold-satellite'; // Satellite (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case scale = 'solar-bold-scale'; // Scale (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case scanner = 'solar-bold-scanner'; // Scanner (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case scanner2 = 'solar-bold-scanner2'; // scanner2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case scissors = 'solar-bold-scissors'; // Scissors (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case scissorsSquare = 'solar-bold-scissors_square'; // scissors_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case scooter = 'solar-bold-scooter'; // Scooter (available in: solar-bold, solar-linear)
    case screenShare = 'solar-bold-screen_share'; // screen_share (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case screencast = 'solar-bold-screencast'; // Screencast (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case screencast2 = 'solar-bold-screencast2'; // screencast2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sdCard = 'solar-bold-sd_card'; // sd_card (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sendSquare = 'solar-bold-send_square'; // send_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sendTwiceSquare = 'solar-bold-send_twice_square'; // send_twice_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case server = 'solar-bold-server'; // Server (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case server2 = 'solar-bold-server2'; // server2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case serverMinimalistic = 'solar-bold-server_minimalistic'; // server_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case serverPath = 'solar-bold-server_path'; // server_path (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case serverSquare = 'solar-bold-server_square'; // server_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case serverSquareCloud = 'solar-bold-server_square_cloud'; // server_square_cloud (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case serverSquareUpdate = 'solar-bold-server_square_update'; // server_square_update (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case settings = 'solar-bold-settings'; // Settings (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case settingsMinimalistic = 'solar-bold-settings_minimalistic'; // settings_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case share = 'solar-bold-share'; // Share (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shareCircle = 'solar-bold-share_circle'; // share_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shield = 'solar-bold-shield'; // Shield (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldCheck = 'solar-bold-shield_check'; // shield_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldCross = 'solar-bold-shield_cross'; // shield_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldKeyhole = 'solar-bold-shield_keyhole'; // shield_keyhole (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldKeyholeMinimalistic = 'solar-bold-shield_keyhole_minimalistic'; // shield_keyhole_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldMinimalistic = 'solar-bold-shield_minimalistic'; // shield_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldMinus = 'solar-bold-shield_minus'; // shield_minus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldNetwork = 'solar-bold-shield_network'; // shield_network (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldPlus = 'solar-bold-shield_plus'; // shield_plus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldStar = 'solar-bold-shield_star'; // shield_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldUp = 'solar-bold-shield_up'; // shield_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldUser = 'solar-bold-shield_user'; // shield_user (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shieldWarning = 'solar-bold-shield_warning'; // shield_warning (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shockAbsorber = 'solar-bold-shock_absorber'; // shock_absorber (available in: solar-bold, solar-linear)
    case shop = 'solar-bold-shop'; // Shop (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shop2 = 'solar-bold-shop2'; // shop2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shopMinimalistic = 'solar-bold-shop_minimalistic'; // shop_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case shuffle = 'solar-bold-shuffle'; // Shuffle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sidebarCode = 'solar-bold-sidebar_code'; // sidebar_code (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sidebarMinimalistic = 'solar-bold-sidebar_minimalistic'; // sidebar_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case siderbar = 'solar-bold-siderbar'; // Siderbar (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case signpost = 'solar-bold-signpost'; // Signpost (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case signpost2 = 'solar-bold-signpost2'; // signpost2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case simCard = 'solar-bold-sim_card'; // sim_card (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case simCardMinimalistic = 'solar-bold-sim_card_minimalistic'; // sim_card_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case simCards = 'solar-bold-sim_cards'; // sim_cards (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case siren = 'solar-bold-siren'; // Siren (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sirenRounded = 'solar-bold-siren_rounded'; // siren_rounded (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case skateboard = 'solar-bold-skateboard'; // Skateboard (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case skateboarding = 'solar-bold-skateboarding'; // Skateboarding (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case skateboardingRound = 'solar-bold-skateboarding_round'; // skateboarding_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case skipNext = 'solar-bold-skip_next'; // skip_next (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case skipPrevious = 'solar-bold-skip_previous'; // skip_previous (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case skirt = 'solar-bold-skirt'; // Skirt (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case slashCircle = 'solar-bold-slash_circle'; // slash_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case slashSquare = 'solar-bold-slash_square'; // slash_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sledgehammer = 'solar-bold-sledgehammer'; // Sledgehammer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sleeping = 'solar-bold-sleeping'; // Sleeping (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sleepingCircle = 'solar-bold-sleeping_circle'; // sleeping_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sleepingSquare = 'solar-bold-sleeping_square'; // sleeping_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sliderHorizontal = 'solar-bold-slider_horizontal'; // slider_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sliderMinimalisticHorizontal = 'solar-bold-slider_minimalistic_horizontal'; // slider_minimalistic_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sliderVertical = 'solar-bold-slider_vertical'; // slider_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sliderVerticalMinimalistic = 'solar-bold-slider_vertical_minimalistic'; // slider_vertical_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartHome = 'solar-bold-smart_home'; // smart_home (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartHomeAngle = 'solar-bold-smart_home_angle'; // smart_home_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartSpeaker = 'solar-bold-smart_speaker'; // smart_speaker (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartSpeaker2 = 'solar-bold-smart_speaker2'; // smart_speaker2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartSpeakerMinimalistic = 'solar-bold-smart_speaker_minimalistic'; // smart_speaker_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartVacuumCleaner = 'solar-bold-smart_vacuum_cleaner'; // smart_vacuum_cleaner (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartVacuumCleaner2 = 'solar-bold-smart_vacuum_cleaner2'; // smart_vacuum_cleaner2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphone = 'solar-bold-smartphone'; // Smartphone (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphone2 = 'solar-bold-smartphone2'; // smartphone2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphoneRotate2 = 'solar-bold-smartphone_rotate2'; // smartphone_rotate2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphoneRotateAngle = 'solar-bold-smartphone_rotate_angle'; // smartphone_rotate_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphoneRotateOrientation = 'solar-bold-smartphone_rotate_orientation'; // smartphone_rotate_orientation (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphoneUpdate = 'solar-bold-smartphone_update'; // smartphone_update (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smartphoneVibration = 'solar-bold-smartphone_vibration'; // smartphone_vibration (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smileCircle = 'solar-bold-smile_circle'; // smile_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case smileSquare = 'solar-bold-smile_square'; // smile_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case snowflake = 'solar-bold-snowflake'; // Snowflake (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case socket = 'solar-bold-socket'; // Socket (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sofa = 'solar-bold-sofa'; // Sofa (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sofa2 = 'solar-bold-sofa2'; // sofa2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sofa3 = 'solar-bold-sofa3'; // sofa3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sort = 'solar-bold-sort'; // Sort (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sortByAlphabet = 'solar-bold-sort_by_alphabet'; // sort_by_alphabet (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sortByTime = 'solar-bold-sort_by_time'; // sort_by_time (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sortFromBottomToTop = 'solar-bold-sort_from_bottom_to_top'; // sort_from_bottom_to_top (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sortFromTopToBottom = 'solar-bold-sort_from_top_to_bottom'; // sort_from_top_to_bottom (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sortHorizontal = 'solar-bold-sort_horizontal'; // sort_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sortVertical = 'solar-bold-sort_vertical'; // sort_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case soundwave = 'solar-bold-soundwave'; // Soundwave (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case soundwaveCircle = 'solar-bold-soundwave_circle'; // soundwave_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case soundwaveSquare = 'solar-bold-soundwave_square'; // soundwave_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case speaker = 'solar-bold-speaker'; // Speaker (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case speakerMinimalistic = 'solar-bold-speaker_minimalistic'; // speaker_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case specialEffects = 'solar-bold-special_effects'; // special_effects (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case spedometerLow = 'solar-bold-spedometer_low'; // spedometer_low (available in: solar-bold, solar-linear)
    case spedometerMax = 'solar-bold-spedometer_max'; // spedometer_max (available in: solar-bold, solar-linear)
    case spedometerMiddle = 'solar-bold-spedometer_middle'; // spedometer_middle (available in: solar-bold, solar-linear)
    case squareAcademicCap = 'solar-bold-square_academic_cap'; // square_academic_cap (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareAcademicCap2 = 'solar-bold-square_academic_cap2'; // square_academic_cap2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareAltArrowDown = 'solar-bold-square_alt_arrow_down'; // square_alt_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareAltArrowLeft = 'solar-bold-square_alt_arrow_left'; // square_alt_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareAltArrowRight = 'solar-bold-square_alt_arrow_right'; // square_alt_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareAltArrowUp = 'solar-bold-square_alt_arrow_up'; // square_alt_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowDown = 'solar-bold-square_arrow_down'; // square_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowLeft = 'solar-bold-square_arrow_left'; // square_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowLeftDown = 'solar-bold-square_arrow_left_down'; // square_arrow_left_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowLeftUp = 'solar-bold-square_arrow_left_up'; // square_arrow_left_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowRight = 'solar-bold-square_arrow_right'; // square_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowRightDown = 'solar-bold-square_arrow_right_down'; // square_arrow_right_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowRightUp = 'solar-bold-square_arrow_right_up'; // square_arrow_right_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareArrowUp = 'solar-bold-square_arrow_up'; // square_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareBottomDown = 'solar-bold-square_bottom_down'; // square_bottom_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareBottomUp = 'solar-bold-square_bottom_up'; // square_bottom_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareDoubleAltArrowDown = 'solar-bold-square_double_alt_arrow_down'; // square_double_alt_arrow_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareDoubleAltArrowLeft = 'solar-bold-square_double_alt_arrow_left'; // square_double_alt_arrow_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareDoubleAltArrowRight = 'solar-bold-square_double_alt_arrow_right'; // square_double_alt_arrow_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareDoubleAltArrowUp = 'solar-bold-square_double_alt_arrow_up'; // square_double_alt_arrow_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareForward = 'solar-bold-square_forward'; // square_forward (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareShareLine = 'solar-bold-square_share_line'; // square_share_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareSortHorizontal = 'solar-bold-square_sort_horizontal'; // square_sort_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareSortVertical = 'solar-bold-square_sort_vertical'; // square_sort_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareTopDown = 'solar-bold-square_top_down'; // square_top_down (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareTopUp = 'solar-bold-square_top_up'; // square_top_up (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareTransferHorizontal = 'solar-bold-square_transfer_horizontal'; // square_transfer_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case squareTransferVertical = 'solar-bold-square_transfer_vertical'; // square_transfer_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ssdRound = 'solar-bold-ssd_round'; // ssd_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ssdSquare = 'solar-bold-ssd_square'; // ssd_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case star = 'solar-bold-star'; // Star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starAngle = 'solar-bold-star_angle'; // star_angle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starCircle = 'solar-bold-star_circle'; // star_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starFall = 'solar-bold-star_fall'; // star_fall (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starFall2 = 'solar-bold-star_fall2'; // star_fall2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starFallMinimalistic = 'solar-bold-star_fall_minimalistic'; // star_fall_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starFallMinimalistic2 = 'solar-bold-star_fall_minimalistic2'; // star_fall_minimalistic2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starRainbow = 'solar-bold-star_rainbow'; // star_rainbow (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starRing = 'solar-bold-star_ring'; // star_ring (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starRings = 'solar-bold-star_rings'; // star_rings (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starShine = 'solar-bold-star_shine'; // star_shine (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stars = 'solar-bold-stars'; // Stars (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starsLine = 'solar-bold-stars_line'; // stars_line (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case starsMinimalistic = 'solar-bold-stars_minimalistic'; // stars_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case station = 'solar-bold-station'; // Station (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stationMinimalistic = 'solar-bold-station_minimalistic'; // station_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stethoscope = 'solar-bold-stethoscope'; // Stethoscope (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stickerCircle = 'solar-bold-sticker_circle'; // sticker_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stickerSmileCircle = 'solar-bold-sticker_smile_circle'; // sticker_smile_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stickerSmileCircle2 = 'solar-bold-sticker_smile_circle2'; // sticker_smile_circle2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stickerSmileSquare = 'solar-bold-sticker_smile_square'; // sticker_smile_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stickerSquare = 'solar-bold-sticker_square'; // sticker_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stop = 'solar-bold-stop'; // Stop (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stopCircle = 'solar-bold-stop_circle'; // stop_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stopwatch = 'solar-bold-stopwatch'; // Stopwatch (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stopwatchPause = 'solar-bold-stopwatch_pause'; // stopwatch_pause (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stopwatchPlay = 'solar-bold-stopwatch_play'; // stopwatch_play (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stream = 'solar-bold-stream'; // Stream (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case streets = 'solar-bold-streets'; // Streets (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case streetsMapPoint = 'solar-bold-streets_map_point'; // streets_map_point (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case streetsNavigation = 'solar-bold-streets_navigation'; // streets_navigation (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stretching = 'solar-bold-stretching'; // Stretching (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case stretchingRound = 'solar-bold-stretching_round'; // stretching_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case structure = 'solar-bold-structure'; // Structure (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case subtitles = 'solar-bold-subtitles'; // Subtitles (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case suitcase = 'solar-bold-suitcase'; // Suitcase (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case suitcaseLines = 'solar-bold-suitcase_lines'; // suitcase_lines (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case suitcaseTag = 'solar-bold-suitcase_tag'; // suitcase_tag (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sun = 'solar-bold-sun'; // Sun (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sun2 = 'solar-bold-sun2'; // sun2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sunFog = 'solar-bold-sun_fog'; // sun_fog (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sunrise = 'solar-bold-sunrise'; // Sunrise (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case sunset = 'solar-bold-sunset'; // Sunset (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case suspension = 'solar-bold-suspension'; // Suspension (available in: solar-bold, solar-linear)
    case suspensionBolt = 'solar-bold-suspension_bolt'; // suspension_bolt (available in: solar-bold, solar-linear)
    case suspensionCross = 'solar-bold-suspension_cross'; // suspension_cross (available in: solar-bold, solar-linear)
    case swimming = 'solar-bold-swimming'; // Swimming (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case syringe = 'solar-bold-syringe'; // Syringe (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tShirt = 'solar-bold-t_shirt'; // t_shirt (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tablet = 'solar-bold-tablet'; // Tablet (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tag = 'solar-bold-tag'; // Tag (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tagHorizontal = 'solar-bold-tag_horizontal'; // tag_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tagPrice = 'solar-bold-tag_price'; // tag_price (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case target = 'solar-bold-target'; // Target (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case teaCup = 'solar-bold-tea_cup'; // tea_cup (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case telescope = 'solar-bold-telescope'; // Telescope (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case temperature = 'solar-bold-temperature'; // Temperature (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tennis = 'solar-bold-tennis'; // Tennis (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tennis2 = 'solar-bold-tennis2'; // tennis2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case testTube = 'solar-bold-test_tube'; // test_tube (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case testTubeMinimalistic = 'solar-bold-test_tube_minimalistic'; // test_tube_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case text = 'solar-bold-text'; // Text (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textBold = 'solar-bold-text_bold'; // text_bold (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textBoldCircle = 'solar-bold-text_bold_circle'; // text_bold_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textBoldSquare = 'solar-bold-text_bold_square'; // text_bold_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textCircle = 'solar-bold-text_circle'; // text_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textCross = 'solar-bold-text_cross'; // text_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textCrossCircle = 'solar-bold-text_cross_circle'; // text_cross_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textCrossSquare = 'solar-bold-text_cross_square'; // text_cross_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textField = 'solar-bold-text_field'; // text_field (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textFieldFocus = 'solar-bold-text_field_focus'; // text_field_focus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textItalic = 'solar-bold-text_italic'; // text_italic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textItalicCircle = 'solar-bold-text_italic_circle'; // text_italic_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textItalicSquare = 'solar-bold-text_italic_square'; // text_italic_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textSelection = 'solar-bold-text_selection'; // text_selection (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textSquare = 'solar-bold-text_square'; // text_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textSquare2 = 'solar-bold-text_square2'; // text_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textUnderline = 'solar-bold-text_underline'; // text_underline (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textUnderlineCircle = 'solar-bold-text_underline_circle'; // text_underline_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case textUnderlineCross = 'solar-bold-text_underline_cross'; // text_underline_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case thermometer = 'solar-bold-thermometer'; // Thermometer (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case threeSquares = 'solar-bold-three_squares'; // three_squares (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tickerStar = 'solar-bold-ticker_star'; // ticker_star (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ticket = 'solar-bold-ticket'; // Ticket (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ticketSale = 'solar-bold-ticket_sale'; // ticket_sale (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case toPip = 'solar-bold-to_pip'; // to_pip (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tornado = 'solar-bold-tornado'; // Tornado (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tornadoSmall = 'solar-bold-tornado_small'; // tornado_small (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case traffic = 'solar-bold-traffic'; // Traffic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case trafficEconomy = 'solar-bold-traffic_economy'; // traffic_economy (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tram = 'solar-bold-tram'; // Tram (available in: solar-bold, solar-linear)
    case transferHorizontal = 'solar-bold-transfer_horizontal'; // transfer_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case transferVertical = 'solar-bold-transfer_vertical'; // transfer_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case translation = 'solar-bold-translation'; // Translation (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case translation2 = 'solar-bold-translation2'; // translation2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case transmission = 'solar-bold-transmission'; // Transmission (available in: solar-bold, solar-linear)
    case transmissionCircle = 'solar-bold-transmission_circle'; // transmission_circle (available in: solar-bold, solar-linear)
    case transmissionSquare = 'solar-bold-transmission_square'; // transmission_square (available in: solar-bold, solar-linear)
    case trashBin2 = 'solar-bold-trash_bin2'; // trash_bin2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case trashBinMinimalistic = 'solar-bold-trash_bin_minimalistic'; // trash_bin_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case trashBinMinimalistic2 = 'solar-bold-trash_bin_minimalistic2'; // trash_bin_minimalistic2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case trashBinTrash = 'solar-bold-trash_bin_trash'; // trash_bin_trash (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case treadmill = 'solar-bold-treadmill'; // Treadmill (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case treadmillRound = 'solar-bold-treadmill_round'; // treadmill_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case trellis = 'solar-bold-trellis'; // Trellis (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tuning = 'solar-bold-tuning'; // Tuning (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tuning2 = 'solar-bold-tuning2'; // tuning2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tuning3 = 'solar-bold-tuning3'; // tuning3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tuning4 = 'solar-bold-tuning4'; // tuning4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tuningSquare = 'solar-bold-tuning_square'; // tuning_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tuningSquare2 = 'solar-bold-tuning_square2'; // tuning_square2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case turntable = 'solar-bold-turntable'; // Turntable (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case turntableMinimalistic = 'solar-bold-turntable_minimalistic'; // turntable_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case turntableMusicNote = 'solar-bold-turntable_music_note'; // turntable_music_note (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case tv = 'solar-bold-tv'; // TV (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ufo = 'solar-bold-ufo'; // UFO (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ufo2 = 'solar-bold-ufo2'; // ufo2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case ufo3 = 'solar-bold-ufo3'; // ufo3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case umbrella = 'solar-bold-umbrella'; // Umbrella (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoLeft = 'solar-bold-undo_left'; // undo_left (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoLeftRound = 'solar-bold-undo_left_round'; // undo_left_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoLeftRoundSquare = 'solar-bold-undo_left_round_square'; // undo_left_round_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoLeftSquare = 'solar-bold-undo_left_square'; // undo_left_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoRight = 'solar-bold-undo_right'; // undo_right (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoRightRound = 'solar-bold-undo_right_round'; // undo_right_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoRightRoundSquare = 'solar-bold-undo_right_round_square'; // undo_right_round_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case undoRightSquare = 'solar-bold-undo_right_square'; // undo_right_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case unread = 'solar-bold-unread'; // Unread (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case upload = 'solar-bold-upload'; // Upload (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case uploadMinimalistic = 'solar-bold-upload_minimalistic'; // upload_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case uploadSquare = 'solar-bold-upload_square'; // upload_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case uploadTrack = 'solar-bold-upload_track'; // upload_track (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case uploadTrack2 = 'solar-bold-upload_track2'; // upload_track2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case uploadTwiceSquare = 'solar-bold-upload_twice_square'; // upload_twice_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case usb = 'solar-bold-usb'; // USB (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case usbCircle = 'solar-bold-usb_circle'; // usb_circle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case usbSquare = 'solar-bold-usb_square'; // usb_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case user = 'solar-bold-user'; // User (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userBlock = 'solar-bold-user_block'; // user_block (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userBlockRounded = 'solar-bold-user_block_rounded'; // user_block_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userCheck = 'solar-bold-user_check'; // user_check (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userCheckRounded = 'solar-bold-user_check_rounded'; // user_check_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userCircle = 'solar-bold-user_circle'; // user_circle (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userCross = 'solar-bold-user_cross'; // user_cross (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userCrossRounded = 'solar-bold-user_cross_rounded'; // user_cross_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userHandUp = 'solar-bold-user_hand_up'; // user_hand_up (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userHands = 'solar-bold-user_hands'; // user_hands (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userHeart = 'solar-bold-user_heart'; // user_heart (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userHeartRounded = 'solar-bold-user_heart_rounded'; // user_heart_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userId = 'solar-bold-user_id'; // user_id (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userMinus = 'solar-bold-user_minus'; // user_minus (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userMinusRounded = 'solar-bold-user_minus_rounded'; // user_minus_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userPlus = 'solar-bold-user_plus'; // user_plus (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userPlusRounded = 'solar-bold-user_plus_rounded'; // user_plus_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userRounded = 'solar-bold-user_rounded'; // user_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userSpeak = 'solar-bold-user_speak'; // user_speak (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case userSpeakRounded = 'solar-bold-user_speak_rounded'; // user_speak_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case usersGroupRounded = 'solar-bold-users_group_rounded'; // users_group_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case usersGroupTwoRounded = 'solar-bold-users_group_two_rounded'; // users_group_two_rounded (available in: solar-bold, solar-bold-duotone, solar-line-duotone, solar-linear, solar-outline)
    case verifiedCheck = 'solar-bold-verified_check'; // verified_check (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFrame = 'solar-bold-video_frame'; // video_frame (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFrame2 = 'solar-bold-video_frame2'; // video_frame2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFrameCut = 'solar-bold-video_frame_cut'; // video_frame_cut (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFrameCut2 = 'solar-bold-video_frame_cut2'; // video_frame_cut2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFramePlayHorizontal = 'solar-bold-video_frame_play_horizontal'; // video_frame_play_horizontal (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFramePlayVertical = 'solar-bold-video_frame_play_vertical'; // video_frame_play_vertical (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoFrameReplace = 'solar-bold-video_frame_replace'; // video_frame_replace (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videoLibrary = 'solar-bold-video_library'; // video_library (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videocamera = 'solar-bold-videocamera'; // Videocamera (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videocameraAdd = 'solar-bold-videocamera_add'; // videocamera_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case videocameraRecord = 'solar-bold-videocamera_record'; // videocamera_record (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case vinyl = 'solar-bold-vinyl'; // Vinyl (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case vinylRecord = 'solar-bold-vinyl_record'; // vinyl_record (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case virus = 'solar-bold-virus'; // Virus (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volleyball = 'solar-bold-volleyball'; // Volleyball (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volleyball2 = 'solar-bold-volleyball2'; // volleyball2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volume = 'solar-bold-volume'; // Volume (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volumeCross = 'solar-bold-volume_cross'; // volume_cross (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volumeKnob = 'solar-bold-volume_knob'; // volume_knob (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volumeLoud = 'solar-bold-volume_loud'; // volume_loud (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case volumeSmall = 'solar-bold-volume_small'; // volume_small (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wadOfMoney = 'solar-bold-wad_of_money'; // wad_of_money (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case walking = 'solar-bold-walking'; // Walking (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case walkingRound = 'solar-bold-walking_round'; // walking_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wallet = 'solar-bold-wallet'; // Wallet (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wallet2 = 'solar-bold-wallet2'; // wallet2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case walletMoney = 'solar-bold-wallet_money'; // wallet_money (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wallpaper = 'solar-bold-wallpaper'; // Wallpaper (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case washingMachine = 'solar-bold-washing_machine'; // washing_machine (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case washingMachineMinimalistic = 'solar-bold-washing_machine_minimalistic'; // washing_machine_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case watchRound = 'solar-bold-watch_round'; // watch_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case watchSquare = 'solar-bold-watch_square'; // watch_square (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case watchSquareMinimalistic = 'solar-bold-watch_square_minimalistic'; // watch_square_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case watchSquareMinimalisticCharge = 'solar-bold-watch_square_minimalistic_charge'; // watch_square_minimalistic_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case water = 'solar-bold-water'; // Water (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case waterSun = 'solar-bold-water_sun'; // water_sun (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case waterdrop = 'solar-bold-waterdrop'; // Waterdrop (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case waterdrops = 'solar-bold-waterdrops'; // Waterdrops (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case weigher = 'solar-bold-weigher'; // Weigher (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wheel = 'solar-bold-wheel'; // Wheel (available in: solar-bold, solar-linear)
    case wheelAngle = 'solar-bold-wheel_angle'; // wheel_angle (available in: solar-bold, solar-linear)
    case whisk = 'solar-bold-whisk'; // Whisk (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wiFiRouter = 'solar-bold-wi_fi_router'; // wi_fi_router (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wiFiRouterMinimalistic = 'solar-bold-wi_fi_router_minimalistic'; // wi_fi_router_minimalistic (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wiFiRouterRound = 'solar-bold-wi_fi_router_round'; // wi_fi_router_round (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widget = 'solar-bold-widget'; // Widget (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widget2 = 'solar-bold-widget2'; // widget2 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widget3 = 'solar-bold-widget3'; // widget3 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widget4 = 'solar-bold-widget4'; // widget4 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widget5 = 'solar-bold-widget5'; // widget5 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widget6 = 'solar-bold-widget6'; // widget6 (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case widgetAdd = 'solar-bold-widget_add'; // widget_add (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case winRar = 'solar-bold-win_rar'; // win_rar (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wind = 'solar-bold-wind'; // Wind (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case windowFrame = 'solar-bold-window_frame'; // window_frame (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wineglass = 'solar-bold-wineglass'; // Wineglass (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wineglassTriangle = 'solar-bold-wineglass_triangle'; // wineglass_triangle (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case wirelessCharge = 'solar-bold-wireless_charge'; // wireless_charge (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case women = 'solar-bold-women'; // Women (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case xxx = 'solar-bold-xxx'; // XXX (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)
    case zipFile = 'solar-bold-zip_file'; // zip_file (available in: solar-bold, solar-bold-duotone, solar-broken, solar-line-duotone, solar-linear, solar-outline)

    /**
     * Get all available icon sets for this icon.
     */
    public function getAvailableSets(): array
    {
        return match ($this) {
            self::accessibility => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::accumulator => ['solar-bold', 'solar-linear'],
            self::addCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::addFolder => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::addSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::adhesivePlaster => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::adhesivePlaster2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbuds => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsCase => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsCaseCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsCaseMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsCaseOpen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::airbudsRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarm => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarmAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarmPause => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarmPlay => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarmRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarmSleep => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alarmTurnOff => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::album => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignBottom => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignHorizontaSpacing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignHorizontalCenter => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignTop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignVerticalCenter => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::alignVerticalSpacing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::altArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::altArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::altArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::altArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archive => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archiveCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archiveDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archiveDownMinimlistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archiveMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archiveUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::archiveUpMinimlistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::armchair => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::armchair2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowLeftDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowLeftUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowRightDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowRightUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowToDownLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowToDownRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowToTopLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowToTopRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::arrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::asteroid => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::atom => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::augmentedReality => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::backpack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::backspace => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bacteria => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bag => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bag2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bag3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bag4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bag5 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bagCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bagCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bagHeart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bagMusic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bagMusic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bagSmile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::balloon => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::balls => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::banknote => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::banknote2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::barChair => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::basketball => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bath => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryChargeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryFull => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryFullMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryHalf => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryHalfMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryLow => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::batteryLowMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bed => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bedsideTable => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bedsideTable2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bedsideTable3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bedsideTable4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bell => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bellBing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bellOff => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::benzeneRing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bicycling => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bicyclingRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bill => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::billCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::billCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::billList => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::blackHole => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::blackHole2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::blackHole3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bluetooth => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bluetoothCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bluetoothSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bluetoothWave => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::body => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bodyShape => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bodyShapeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bolt => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::boltCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bomb => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bombEmoji => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bombMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bone => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::boneBroken => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::boneCrack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bones => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bonfire => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::book => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::book2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookBookmark => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookBookmarkMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookmark => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookmarkCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookmarkOpened => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookmarkSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bookmarkSquareMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::boombox => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bottle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bowling => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::box => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::boxMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::branchingPathsDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::branchingPathsUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::broom => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bug => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::bugMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::buildings => ['solar-bold', 'solar-linear'],
            self::buildings2 => ['solar-bold', 'solar-linear'],
            self::buildings3 => ['solar-bold', 'solar-linear'],
            self::bus => ['solar-bold', 'solar-linear'],
            self::calculator => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calculatorMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calendar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calendarAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calendarDate => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calendarMark => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calendarMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::calendarSearch => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callCancel => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callCancelRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callChat => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callChatRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callDropped => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callDroppedRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callMedicine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::callMedicineRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::camera => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cameraAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cameraMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cameraRotate => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cameraSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::card => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::card2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cardRecive => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cardSearch => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cardSend => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cardTransfer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cardholder => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cart2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cart3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cart4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cart5 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartLarge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartLarge2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartLarge3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartLarge4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartLargeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cartPlus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::case => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::caseMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::caseRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::caseRoundMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cashOut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cassette => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cassette2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cat => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chair => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chair2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chandelier => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chart2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chartSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatDots => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundCall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundDots => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundLike => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundMoney => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundUnread => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatRoundVideo => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquareArrow => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquareCall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquareCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquareCode => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatSquareLike => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chatUnread => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::checkCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::checkRead => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::checkSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::checklist => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::checklistMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chefHat => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chefHatHeart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::chefHatMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::circleBottomDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::circleBottomUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::circleTopDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::circleTopUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::city => ['solar-bold', 'solar-linear'],
            self::clapperboard => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clapperboardEdit => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clapperboardOpen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clapperboardOpenPlay => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clapperboardPlay => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clapperboardText => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboard => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboardAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboardCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboardHeart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboardList => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboardRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clipboardText => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clockCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clockSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::closeCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::closeSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::closet => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::closet2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloud => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudBolt => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudBoltMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudDownload => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudFile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudMinus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudPlus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudRain => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudSnowfall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudSnowfallMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudStorage => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudStorm => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudSun => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudSun2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudUpload => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudWaterdrop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudWaterdrops => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::clouds => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloudyMoon => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cloundCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::code => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::code2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::codeCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::codeFile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::codeScan => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::codeSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::colourTuneing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::command => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::compass => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::compassBig => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::compassSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::condicioner => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::condicioner2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::confetti => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::confettiMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::confoundedCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::confoundedSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::copy => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::copyright => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::corkscrew => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cosmetic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::courseDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::courseUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cpu => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cpuBolt => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::creativeCommons => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::crop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cropMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::crown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::crownLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::crownMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::crownStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cup => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cupFirst => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cupHot => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cupMusic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cupPaper => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cupStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cursor => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::cursorSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::danger => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dangerCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dangerSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dangerTriangle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::database => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::delivery => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::devices => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::diagramDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::diagramUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dialog => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dialog2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::diploma => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::diplomaVerified => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::diskette => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dislike => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::display => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dna => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::document => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::documentAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::documentMedicine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::documentText => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::documents => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::documentsMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dollar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dollarMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::donut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::donutBitten => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::doubleAltArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::doubleAltArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::doubleAltArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::doubleAltArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::download => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::downloadMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::downloadSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::downloadTwiceSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dropper => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dropper2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dropper3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dropperMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dropperMinimalistic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dumbbell => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dumbbellLarge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dumbbellLargeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dumbbellSmall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dumbbells => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::dumbbells2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::earth => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::electricRefueling => ['solar-bold', 'solar-linear'],
            self::emojiFunnyCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::emojiFunnySquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::endCall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::endCallRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::eraser => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::eraserCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::eraserSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::euro => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::exit => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::explicit => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::export => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::expressionlessCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::expressionlessSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::eye => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::eyeClosed => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::eyeScan => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::faceScanCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::faceScanSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::facemaskCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::facemaskSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::feed => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ferrisWheel => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::figma => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::figmaFile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::file => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileCorrupted => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileDownload => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileFavourite => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileSend => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileSmile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fileText => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::filter => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::filters => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fire => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fireMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fireSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flag => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flag2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flame => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flashDrive => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flashlight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flashlightOn => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flipHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::flipVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::floorLamp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::floorLampMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fog => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folder => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folder2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderCloud => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderError => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderFavouritebookmark => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderFavouritestar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderOpen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderPathConnect => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderSecurity => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::folderWithFiles => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::football => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::forbidden => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::forbiddenCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::forward => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::forward2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fridge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fuel => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fullScreen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fullScreenCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::fullScreenSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gallery => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryDownload => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryEdit => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryFavourite => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gallerySend => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::galleryWide => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gameboy => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gamepad => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gamepadCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gamepadMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gamepadNoCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gamepadOld => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::garage => ['solar-bold', 'solar-linear'],
            self::gasStation => ['solar-bold', 'solar-linear'],
            self::ghost => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ghostSmile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gift => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::glasses => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::global => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::globus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::golf => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::gps => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::graph => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::graphDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::graphDownNew => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::graphNew => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::graphNewUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::graphUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hamburgerMenu => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::handHeart => ['solar-bold', 'solar-linear'],
            self::handMoney => ['solar-bold', 'solar-linear'],
            self::handPills => ['solar-bold', 'solar-linear'],
            self::handShake => ['solar-bold', 'solar-linear'],
            self::handStars => ['solar-bold', 'solar-linear'],
            self::hanger => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hanger2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hashtag => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hashtagChat => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hashtagCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hashtagSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::headphonesRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::headphonesRoundSound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::headphonesSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::headphonesSquareSound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::health => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartBroken => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartLock => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartPulse => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartPulse2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartShine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::heartUnlock => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hearts => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::help => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::highDefinition => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::highQuality => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hiking => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hikingMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hikingRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::history => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::history2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::history3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::home => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::home2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeAddAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeAngle2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeSmile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeSmileAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeWifi => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::homeWifiAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hospital => ['solar-bold', 'solar-linear'],
            self::hourglass => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::hourglassLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::iPhone => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::icon4K => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::import => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::inbox => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::inboxArchive => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::inboxIn => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::inboxLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::inboxOut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::inboxUnread => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::incognito => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::incomingCall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::incomingCallRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::infinity => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::infoCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::infoSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::jarOfPills => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::jarOfPills2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::key => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keyMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keyMinimalistic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keyMinimalisticSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keyMinimalisticSquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keyMinimalisticSquare3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keySquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keySquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::keyboard => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::kickScooter => ['solar-bold', 'solar-linear'],
            self::ladle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lamp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::laptop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::laptop2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::laptop3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::laptopMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::layers => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::layersMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::leaf => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::letter => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::letterOpened => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::letterUnread => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::library => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lightbulb => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lightbulbBolt => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lightbulbMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lightning => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::like => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::link => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkBroken => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkBrokenMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkMinimalistic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkRoundAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::linkSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::list => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::list1 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listArrowDownMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listArrowUpMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listCheckMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listCrossMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listDownMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listHeart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listHeartMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::listUpMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lock => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockKeyhole => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockKeyholeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockKeyholeMinimalisticUnlocked => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockKeyholeUnlocked => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockPassword => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockPasswordUnlocked => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::lockUnlocked => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::login => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::login2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::login3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::logout => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::logout2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::logout3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magicStick => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magicStick2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magicStick3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magnet => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magnetWave => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magnifer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magniferBug => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magniferZoomIn => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::magniferZoomOut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mailbox => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::map => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapArrowSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPoint => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointFavourite => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointHospital => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointRotate => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointSchool => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointSearch => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mapPointWave => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maskHapply => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maskSad => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::masks => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maximize => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maximizeSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maximizeSquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maximizeSquare3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::maximizeSquareMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medalRibbon => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medalRibbonStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medalRibbonsStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medalStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medalStarCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medalStarSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::medicalKit => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::meditation => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::meditationRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::men => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mentionCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mentionSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::menuDots => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::menuDotsCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::menuDotsSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::microphone => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::microphone2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::microphone3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::microphoneLarge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimalisticMagnifer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimalisticMagniferBug => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimalisticMagniferZoomIn => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimalisticMagniferZoomOut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimize => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimizeSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimizeSquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimizeSquare3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minimizeSquareMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minusCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::minusSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mirror => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mirrorLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mirrorRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::moneyBag => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::monitor => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::monitorCamera => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::monitorSmartphone => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::moon => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::moonFog => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::moonSleep => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::moonStars => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mouse => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mouseCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::mouseMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::moveToFolder => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::multipleForwardLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::multipleForwardRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicLibrary => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicLibrary2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNote => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNote2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNote3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNote4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNoteSlider => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNoteSlider2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::musicNotes => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::muted => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notebook => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notebookBookmark => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notebookMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notebookSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notes => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notesMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notificationLinesRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notificationRemove => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notificationUnread => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::notificationUnreadLines => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::objectScan => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::outgoingCall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::outgoingCallRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ovenMitts => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ovenMittsMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paintRoller => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::palette => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paletteRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pallete2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::panorama => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paperBin => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paperclip => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paperclip2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paperclipRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paperclipRounded2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paragraphSpacing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::passport => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::passportMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::password => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::passwordMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::passwordMinimalisticInput => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pause => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pauseCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::paw => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pen2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::penNewRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::penNewSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::peopleNearby => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::perfume => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::phone => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::phoneCalling => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::phoneCallingRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::phoneRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pieChart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pieChart2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pieChart3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pill => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pills => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pills2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pills3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pin => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pinCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pinList => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pip => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pip2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pipette => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plaaylistMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plain => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plain2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plain3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::planet => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::planet2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::planet3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::planet4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plate => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::play => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playStream => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playbackSpeed => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playlist => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playlist2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playlistMinimalistic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::playlistMinimalistic3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plugCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::plusMinus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::podcast => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pointOnMap => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pointOnMapPerspective => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::postsCarouselHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::postsCarouselVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::power => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::presentationGraph => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::printer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::printer2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::printerMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::programming => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::projector => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pulse => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::pulse2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::qrCode => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::questionCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::questionSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::quitFullScreen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::quitFullScreenCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::quitFullScreenSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::quitPip => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::radar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::radar2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::radialBlur => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::radio => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::radioMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ranking => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reciveSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reciveTwiceSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::record => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::recordCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::recordMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::recordSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reel => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reel2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::refresh => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::refreshCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::refreshSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::remoteController => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::remoteController2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::remoteControllerMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::removeFolder => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reorder => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::repeat => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::repeatOne => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::repeatOneMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reply => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::reply2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::restart => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::restartCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::restartSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::revote => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewind10SecondsBack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewind10SecondsForward => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewind15SecondsBack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewind15SecondsForward => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewind5SecondsBack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewind5SecondsForward => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewindBack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewindBackCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewindForward => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rewindForwardCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rocket => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rocket2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rollingPin => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundAltArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundAltArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundAltArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundAltArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowLeftDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowLeftUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowRightDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowRightUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundDoubleAltArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundDoubleAltArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundDoubleAltArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundDoubleAltArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundGraph => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundSortHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundSortVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundTransferDiagonal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundTransferHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundTransferVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundedMagnifer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundedMagniferBug => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundedMagniferZoomIn => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::roundedMagniferZoomOut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::route => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::routing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::routing2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::routing3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ruble => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rugby => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ruler => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rulerAngular => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rulerCrossPen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::rulerPen => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::running => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::running2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::runningRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sadCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sadSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::safe2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::safeCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::safeSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sale => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::saleSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::satellite => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::scale => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::scanner => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::scanner2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::scissors => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::scissorsSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::scooter => ['solar-bold', 'solar-linear'],
            self::screenShare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::screencast => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::screencast2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sdCard => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sendSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sendTwiceSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::server => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::server2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::serverMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::serverPath => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::serverSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::serverSquareCloud => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::serverSquareUpdate => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::settings => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::settingsMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::share => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shareCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shield => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldKeyhole => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldKeyholeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldMinus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldNetwork => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldPlus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldUser => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shieldWarning => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shockAbsorber => ['solar-bold', 'solar-linear'],
            self::shop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shop2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shopMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::shuffle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sidebarCode => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sidebarMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::siderbar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::signpost => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::signpost2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::simCard => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::simCardMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::simCards => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::siren => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sirenRounded => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::skateboard => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::skateboarding => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::skateboardingRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::skipNext => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::skipPrevious => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::skirt => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::slashCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::slashSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sledgehammer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sleeping => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sleepingCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sleepingSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sliderHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sliderMinimalisticHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sliderVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sliderVerticalMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartHome => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartHomeAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartSpeaker => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartSpeaker2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartSpeakerMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartVacuumCleaner => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartVacuumCleaner2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphone => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphone2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphoneRotate2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphoneRotateAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphoneRotateOrientation => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphoneUpdate => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smartphoneVibration => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smileCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::smileSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::snowflake => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::socket => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sofa => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sofa2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sofa3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sort => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sortByAlphabet => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sortByTime => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sortFromBottomToTop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sortFromTopToBottom => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sortHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sortVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::soundwave => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::soundwaveCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::soundwaveSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::speaker => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::speakerMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::specialEffects => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::spedometerLow => ['solar-bold', 'solar-linear'],
            self::spedometerMax => ['solar-bold', 'solar-linear'],
            self::spedometerMiddle => ['solar-bold', 'solar-linear'],
            self::squareAcademicCap => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareAcademicCap2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareAltArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareAltArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareAltArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareAltArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowLeftDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowLeftUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowRightDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowRightUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareBottomDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareBottomUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareDoubleAltArrowDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareDoubleAltArrowLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareDoubleAltArrowRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareDoubleAltArrowUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareForward => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareShareLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareSortHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareSortVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareTopDown => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareTopUp => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareTransferHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::squareTransferVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ssdRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ssdSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::star => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starAngle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starFall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starFall2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starFallMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starFallMinimalistic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starRainbow => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starRing => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starRings => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starShine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stars => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starsLine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::starsMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::station => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stationMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stethoscope => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stickerCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stickerSmileCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stickerSmileCircle2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stickerSmileSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stickerSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stopCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stopwatch => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stopwatchPause => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stopwatchPlay => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stream => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::streets => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::streetsMapPoint => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::streetsNavigation => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stretching => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::stretchingRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::structure => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::subtitles => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::suitcase => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::suitcaseLines => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::suitcaseTag => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sun => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sun2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sunFog => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sunrise => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::sunset => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::suspension => ['solar-bold', 'solar-linear'],
            self::suspensionBolt => ['solar-bold', 'solar-linear'],
            self::suspensionCross => ['solar-bold', 'solar-linear'],
            self::swimming => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::syringe => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tShirt => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tablet => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tag => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tagHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tagPrice => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::target => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::teaCup => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::telescope => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::temperature => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tennis => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tennis2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::testTube => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::testTubeMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::text => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textBold => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textBoldCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textBoldSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textCrossCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textCrossSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textField => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textFieldFocus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textItalic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textItalicCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textItalicSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textSelection => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textSquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textUnderline => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textUnderlineCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::textUnderlineCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::thermometer => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::threeSquares => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tickerStar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ticket => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ticketSale => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::toPip => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tornado => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tornadoSmall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::traffic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::trafficEconomy => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tram => ['solar-bold', 'solar-linear'],
            self::transferHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::transferVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::translation => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::translation2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::transmission => ['solar-bold', 'solar-linear'],
            self::transmissionCircle => ['solar-bold', 'solar-linear'],
            self::transmissionSquare => ['solar-bold', 'solar-linear'],
            self::trashBin2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::trashBinMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::trashBinMinimalistic2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::trashBinTrash => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::treadmill => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::treadmillRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::trellis => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tuning => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tuning2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tuning3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tuning4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tuningSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tuningSquare2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::turntable => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::turntableMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::turntableMusicNote => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::tv => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ufo => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ufo2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::ufo3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::umbrella => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoLeft => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoLeftRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoLeftRoundSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoLeftSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoRight => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoRightRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoRightRoundSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::undoRightSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::unread => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::upload => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::uploadMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::uploadSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::uploadTrack => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::uploadTrack2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::uploadTwiceSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::usb => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::usbCircle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::usbSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::user => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userBlock => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userBlockRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userCheck => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userCheckRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userCircle => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userCross => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userCrossRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userHandUp => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userHands => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userHeart => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userHeartRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userId => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userMinus => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userMinusRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userPlus => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userPlusRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userSpeak => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::userSpeakRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::usersGroupRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::usersGroupTwoRounded => ['solar-bold', 'solar-bold-duotone', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::verifiedCheck => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFrame => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFrame2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFrameCut => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFrameCut2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFramePlayHorizontal => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFramePlayVertical => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoFrameReplace => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videoLibrary => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videocamera => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videocameraAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::videocameraRecord => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::vinyl => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::vinylRecord => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::virus => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volleyball => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volleyball2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volume => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volumeCross => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volumeKnob => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volumeLoud => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::volumeSmall => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wadOfMoney => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::walking => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::walkingRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wallet => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wallet2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::walletMoney => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wallpaper => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::washingMachine => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::washingMachineMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::watchRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::watchSquare => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::watchSquareMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::watchSquareMinimalisticCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::water => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::waterSun => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::waterdrop => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::waterdrops => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::weigher => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wheel => ['solar-bold', 'solar-linear'],
            self::wheelAngle => ['solar-bold', 'solar-linear'],
            self::whisk => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wiFiRouter => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wiFiRouterMinimalistic => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wiFiRouterRound => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widget => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widget2 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widget3 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widget4 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widget5 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widget6 => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::widgetAdd => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::winRar => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wind => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::windowFrame => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wineglass => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wineglassTriangle => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::wirelessCharge => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::women => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::xxx => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
            self::zipFile => ['solar-bold', 'solar-bold-duotone', 'solar-broken', 'solar-line-duotone', 'solar-linear', 'solar-outline'],
        };
    }

    /**
     * Get the icon name without the set prefix.
     */
    public function getIconName(): string
    {
        return substr($this->value, strpos($this->value, '-', 6) + 1);
    }

    /**
     * Get the primary icon set for this icon.
     */
    public function getPrimarySet(): string
    {
        return substr($this->value, 0, strpos($this->value, '-', 6));
    }

    /**
     * Check if this icon is available in a specific set.
     */
    public function isAvailableIn(string $set): bool
    {
        return in_array($set, $this->getAvailableSets());
    }

    /**
     * Get the icon value for a specific set.
     */
    public function forSet(string $set): string
    {
        if (!$this->isAvailableIn($set)) {
            throw new \InvalidArgumentException("Icon {$this->name} is not available in set {$set}");
        }

        return $set . '-' . strtolower($this->getIconName());
    }
}