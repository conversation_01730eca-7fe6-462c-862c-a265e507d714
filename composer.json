{"name": "monsefeledrisse/laravel-solar-icons", "description": "Solar Icon Set for Laravel - Professional icon package with 7,000+ Solar icons across 6 styles", "type": "library", "keywords": ["laravel", "icons", "solar", "blade-icons", "ui", "svg", "blade-components"], "homepage": "https://github.com/monsefrock/laravel-solar-icons", "license": "MIT", "autoload": {"psr-4": {"Monsefeledrisse\\LaravelSolarIcons\\": "src/"}}, "autoload-dev": {"psr-4": {"Monsefeledrisse\\LaravelSolarIcons\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "support": {"issues": "https://github.com/monsefrock/laravel-solar-icons/issues", "source": "https://github.com/monsefrock/laravel-solar-icons"}, "require": {"blade-ui-kit/blade-icons": "^1.8", "php": "^8.1", "illuminate/support": "^9.0|^10.0|^11.0|^12.0"}, "require-dev": {"pestphp/pest": "^2.0", "orchestra/testbench": "^8.0|^9.0", "phpunit/phpunit": "^10.0"}, "scripts": {"test": "pest", "generate-enum": "php bin/generate-solar-enum.php", "version": "php bin/version", "version:current": "php bin/version current", "version:patch": "php bin/version bump patch", "version:minor": "php bin/version bump minor", "version:major": "php bin/version bump major"}, "extra": {"laravel": {"providers": ["Monsefeledrisse\\LaravelSolarIcons\\SolarIconSetServiceProvider"]}}, "minimum-stability": "beta", "prefer-stable": false, "config": {"allow-plugins": {"pestphp/pest-plugin": true}}}