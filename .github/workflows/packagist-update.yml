name: Update Packagist

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  update-packagist:
    runs-on: ubuntu-latest
    
    steps:
    - name: Update Packagist
      run: |
        curl -XPOST -H'content-type:application/json' \
          'https://packagist.org/api/update-package?username=${{ secrets.PACKAGIST_USERNAME }}&apiToken=${{ secrets.PACKAGIST_TOKEN }}' \
          -d'{"repository":{"url":"https://github.com/${{ github.repository }}"}}'
